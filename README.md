# 🎮 RAGE MP Roleplay Server - نسل جدید

## 📋 درباره پروژه

این پروژه یک سرور رول‌پلی پیشرفته و واقع‌گرایانه بر بستر RAGE Multiplayer است که با تکنولوژی‌های مدرن و معماری حرفه‌ای طراحی شده است.

## 🏗️ معماری پروژه

### Backend (Server-side)
- **زبان**: TypeScript/Node.js
- **معماری**: Modular & Scalable
- **پایگاه داده**: MySQL/PostgreSQL با ORM
- **استانداردها**: Clean Code, ESLint, Prettier

### Frontend (Client-side UI)
- **فریمورک**: React.js با TypeScript
- **Build Tool**: Vite
- **طراحی**: Modern, Responsive, Minimal

## 📁 ساختار پروژه

```
├── packages/
│   └── server/                 # Backend سرور
│       ├── src/
│       │   ├── modules/        # ماژول‌های سیستم (اقتصاد، مشاغل، فکشن‌ها)
│       │   ├── core/           # هسته اصلی سرور
│       │   ├── database/       # مدیریت پایگاه داده
│       │   ├── events/         # مدیریت رویدادها
│       │   ├── utils/          # ابزارهای کمکی
│       │   └── types/          # تعریف انواع TypeScript
│       └── tests/              # تست‌های واحد
├── client_packages/
│   └── ui/                     # Frontend رابط کاربری
│       ├── src/
│       │   ├── components/     # کامپوننت‌های React
│       │   ├── pages/          # صفحات اصلی
│       │   ├── hooks/          # Custom Hooks
│       │   ├── utils/          # ابزارهای کمکی
│       │   ├── types/          # تعریف انواع TypeScript
│       │   └── assets/         # فایل‌های استاتیک
│       └── public/             # فایل‌های عمومی
├── maps/                       # نقشه‌های سفارشی
├── plugins/                    # پلاگین‌های اضافی
└── conf.json                   # تنظیمات سرور RAGE MP
```

## 🚀 ویژگی‌های کلیدی

### 🎯 گیم‌پلی واقع‌گرایانه
- سیستم اقتصادی پویا و پیچیده
- مشاغل متنوع با مسیرهای شغلی
- سیستم قضایی و دولتی کامل
- تعاملات اجتماعی عمیق
- شخصی‌سازی بی‌نظیر

### 🎨 رابط کاربری مدرن
- طراحی مینیمال و زیبا
- عملکرد بهینه (بدون تأثیر بر FPS)
- واکنش‌گرا و سریع
- یکپارچگی بصری

### 🔧 معماری فنی
- کد تمیز و منظم
- ماژولار و قابل توسعه
- امنیت بالا
- مستندسازی کامل

## 🛠️ نصب و راه‌اندازی

### پیش‌نیازها
- Node.js (v18+)
- MySQL/PostgreSQL
- RAGE MP Server

### مراحل نصب
```bash
# کلون پروژه
git clone [repository-url]
cd sese

# نصب وابستگی‌های سرور
cd packages/server
npm install

# نصب وابستگی‌های UI
cd ../client_packages/ui
npm install

# راه‌اندازی پایگاه داده
npm run db:setup

# اجرای سرور توسعه
npm run dev
```

## 📝 مشارکت

برای مشارکت در پروژه، لطفاً ابتدا [راهنمای مشارکت](CONTRIBUTING.md) را مطالعه کنید.

## 📄 مجوز

این پروژه تحت مجوز MIT منتشر شده است.

---

**توسعه‌یافته با ❤️ برای جامعه RAGE MP**
