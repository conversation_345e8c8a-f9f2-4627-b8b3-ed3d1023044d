{"name": "@rage-rp/server", "version": "1.0.0", "description": "RAGE MP Roleplay Server - Backend", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "db:migrate": "tsx src/database/migrate.ts", "db:seed": "tsx src/database/seed.ts", "db:reset": "tsx src/database/reset.ts"}, "keywords": ["rage-mp", "roleplay", "typescript", "nodejs", "gaming"], "author": "RAGE RP Team", "license": "MIT", "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^20.11.24", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "prettier": "^3.2.5", "rimraf": "^5.0.5", "ts-jest": "^29.1.2", "tsx": "^4.7.1", "typescript": "^5.4.2"}, "dependencies": {"bcryptjs": "^2.4.3", "dotenv": "^16.4.5", "joi": "^17.12.2", "jsonwebtoken": "^9.0.2", "mysql2": "^3.9.2", "reflect-metadata": "^0.2.1", "typeorm": "^0.3.20", "winston": "^3.12.0", "zod": "^3.22.4"}, "engines": {"node": ">=18.0.0"}}