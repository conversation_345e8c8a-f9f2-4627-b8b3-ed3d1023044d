import winston from 'winston';
import path from 'path';

/**
 * Centralized logging system using Winston
 * Supports multiple transports and log levels
 */
export class Logger {
  private static instance: Logger;
  private readonly winston: winston.Logger;

  private constructor() {
    this.winston = winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: winston.format.combine(
        winston.format.timestamp({
          format: 'YYYY-MM-DD HH:mm:ss'
        }),
        winston.format.errors({ stack: true }),
        winston.format.colorize({ all: true }),
        winston.format.printf(({ timestamp, level, message, stack }) => {
          return `${timestamp} [${level}]: ${message}${stack ? '\n' + stack : ''}`;
        })
      ),
      transports: this.createTransports(),
      exitOnError: false,
    });

    // Handle uncaught exceptions and rejections
    this.winston.exceptions.handle(
      new winston.transports.File({ 
        filename: path.join(process.cwd(), 'logs', 'exceptions.log') 
      })
    );

    this.winston.rejections.handle(
      new winston.transports.File({ 
        filename: path.join(process.cwd(), 'logs', 'rejections.log') 
      })
    );
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  /**
   * Create Winston transports based on environment
   */
  private createTransports(): winston.transport[] {
    const transports: winston.transport[] = [];

    // Console transport
    if (process.env.LOG_CONSOLE !== 'false') {
      transports.push(
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple()
          )
        })
      );
    }

    // File transports
    if (process.env.LOG_FILE !== 'false') {
      // General log file
      transports.push(
        new winston.transports.File({
          filename: path.join(process.cwd(), 'logs', 'combined.log'),
          maxsize: 5242880, // 5MB
          maxFiles: 5,
        })
      );

      // Error log file
      transports.push(
        new winston.transports.File({
          filename: path.join(process.cwd(), 'logs', 'error.log'),
          level: 'error',
          maxsize: 5242880, // 5MB
          maxFiles: 5,
        })
      );

      // Debug log file (only in development)
      if (process.env.NODE_ENV === 'development') {
        transports.push(
          new winston.transports.File({
            filename: path.join(process.cwd(), 'logs', 'debug.log'),
            level: 'debug',
            maxsize: 5242880, // 5MB
            maxFiles: 3,
          })
        );
      }
    }

    return transports;
  }

  /**
   * Log an info message
   */
  public info(message: string, meta?: unknown): void {
    this.winston.info(message, meta);
  }

  /**
   * Log an error message
   */
  public error(message: string, error?: Error | unknown): void {
    if (error instanceof Error) {
      this.winston.error(message, { error: error.message, stack: error.stack });
    } else {
      this.winston.error(message, { error });
    }
  }

  /**
   * Log a warning message
   */
  public warn(message: string, meta?: unknown): void {
    this.winston.warn(message, meta);
  }

  /**
   * Log a debug message
   */
  public debug(message: string, meta?: unknown): void {
    this.winston.debug(message, meta);
  }

  /**
   * Log a verbose message
   */
  public verbose(message: string, meta?: unknown): void {
    this.winston.verbose(message, meta);
  }

  /**
   * Log a silly message
   */
  public silly(message: string, meta?: unknown): void {
    this.winston.silly(message, meta);
  }

  /**
   * Create a child logger with additional metadata
   */
  public child(meta: Record<string, unknown>): winston.Logger {
    return this.winston.child(meta);
  }

  /**
   * Set log level dynamically
   */
  public setLevel(level: string): void {
    this.winston.level = level;
  }

  /**
   * Get current log level
   */
  public getLevel(): string {
    return this.winston.level;
  }

  /**
   * Log player action for audit trail
   */
  public logPlayerAction(
    playerId: number, 
    action: string, 
    details?: Record<string, unknown>
  ): void {
    this.info(`Player Action: ${action}`, {
      playerId,
      action,
      details,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Log admin action for audit trail
   */
  public logAdminAction(
    adminId: number,
    action: string,
    targetId?: number,
    details?: Record<string, unknown>
  ): void {
    this.warn(`Admin Action: ${action}`, {
      adminId,
      action,
      targetId,
      details,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Log system event
   */
  public logSystemEvent(
    event: string,
    details?: Record<string, unknown>
  ): void {
    this.info(`System Event: ${event}`, {
      event,
      details,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Log security event
   */
  public logSecurityEvent(
    event: string,
    playerId?: number,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
    details?: Record<string, unknown>
  ): void {
    const logMethod = severity === 'critical' || severity === 'high' ? 'error' : 'warn';
    
    this[logMethod](`Security Event: ${event}`, {
      event,
      playerId,
      severity,
      details,
      timestamp: new Date().toISOString(),
    });
  }
}
