@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Inter font */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Base styles */
@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-transparent text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
}

/* Component styles */
@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-outline {
    @apply btn border border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-500;
  }
  
  .btn-ghost {
    @apply btn text-secondary-700 hover:bg-secondary-100 focus:ring-secondary-500;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }
  
  /* Input styles */
  .input {
    @apply block w-full rounded-lg border border-secondary-300 bg-white px-3 py-2 text-sm placeholder-secondary-400 shadow-sm transition-colors focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 disabled:bg-secondary-50 disabled:text-secondary-500;
  }
  
  .input-error {
    @apply border-danger-500 focus:border-danger-500 focus:ring-danger-500;
  }
  
  /* Card styles */
  .card {
    @apply rounded-xl bg-white shadow-soft border border-secondary-200;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-secondary-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-secondary-200;
  }
  
  /* Glass morphism effect */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }
  
  .glass-dark {
    @apply bg-black/20 backdrop-blur-md border border-white/10;
  }
  
  /* HUD styles */
  .hud-element {
    @apply glass-dark rounded-lg p-3 text-white shadow-lg;
  }
  
  .hud-bar {
    @apply h-2 rounded-full overflow-hidden bg-black/30;
  }
  
  .hud-bar-fill {
    @apply h-full transition-all duration-300 ease-out;
  }
  
  /* Modal styles */
  .modal-overlay {
    @apply fixed inset-0 bg-black/50 backdrop-blur-sm z-50;
  }
  
  .modal-content {
    @apply fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full max-w-md mx-4;
  }
  
  /* Notification styles */
  .notification {
    @apply glass-dark rounded-lg p-4 text-white shadow-lg border border-white/20;
  }
  
  /* Chat styles */
  .chat-container {
    @apply glass-dark rounded-lg p-4 max-h-96 overflow-y-auto;
  }
  
  .chat-message {
    @apply mb-2 p-2 rounded text-sm;
  }
  
  .chat-message.system {
    @apply text-yellow-400;
  }
  
  .chat-message.ooc {
    @apply text-blue-400;
  }
  
  .chat-message.admin {
    @apply text-red-400;
  }
  
  /* Inventory styles */
  .inventory-grid {
    @apply grid grid-cols-5 gap-2 p-4;
  }
  
  .inventory-slot {
    @apply aspect-square bg-black/30 border border-white/20 rounded-lg flex items-center justify-center cursor-pointer hover:bg-white/10 transition-colors;
  }
  
  .inventory-slot.occupied {
    @apply bg-primary-600/20 border-primary-400/50;
  }
  
  /* Vehicle HUD styles */
  .vehicle-hud {
    @apply fixed bottom-4 right-4 glass-dark rounded-lg p-4 text-white;
  }
  
  .speedometer {
    @apply text-2xl font-bold text-primary-400;
  }
  
  /* Loading styles */
  .loading-dots {
    @apply inline-flex space-x-1;
  }
  
  .loading-dots > div {
    @apply w-2 h-2 bg-current rounded-full animate-pulse;
  }
  
  .loading-dots > div:nth-child(2) {
    animation-delay: 0.2s;
  }
  
  .loading-dots > div:nth-child(3) {
    animation-delay: 0.4s;
  }
}

/* Utility styles */
@layer utilities {
  /* Text utilities */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
  
  /* Responsive text */
  .text-responsive {
    @apply text-sm md:text-base lg:text-lg;
  }
  
  /* Pointer events */
  .pointer-events-none {
    pointer-events: none;
  }
  
  .pointer-events-auto {
    pointer-events: auto;
  }
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-secondary-100 rounded-full;
}

::-webkit-scrollbar-thumb {
  @apply bg-secondary-400 rounded-full hover:bg-secondary-500;
}

/* Focus styles for accessibility */
.focus-visible {
  @apply outline-none ring-2 ring-primary-500 ring-offset-2;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
