{"version": 3, "sources": ["../../src/util/OrmUtils.ts"], "names": [], "mappings": ";;;AAMA,MAAa,QAAQ;IACjB,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACI,MAAM,CAAC,KAAK,CAAI,KAAU,EAAE,IAAY;QAC3C,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC9D,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,CAAA;QACjD,CAAC,CAAC,CAAA;IACN,CAAC;IAEM,MAAM,CAAC,sBAAsB,CAChC,iBAAiC;QAEjC,OAAO;YACH,iBAAiB,CAAC,MAAM,CACpB,CAAC,GAAG,EAAY,EAAE,CAAC,OAAO,GAAG,KAAK,QAAQ,CAC7C;YACD,iBAAiB,CAAC,MAAM,CACpB,CAAC,GAAG,EAAiB,EAAE,CAAC,OAAO,GAAG,KAAK,QAAQ,CAClD;SACJ,CAAA;IACL,CAAC;IAEM,MAAM,CAAC,OAAO,CACjB,KAAU,EACV,gBAAgC;QAEhC,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;YACxC,MAAM,GAAG,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAA;YACnC,IAAI,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAA;YACpD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,OAAO,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,EAAE,CAAA;gBAChC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC9B,CAAC;YACD,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACzB,OAAO,YAAY,CAAA;QACvB,CAAC,EAAE,EAAkC,CAAC,CAAA;IAC1C,CAAC;IAIM,MAAM,CAAC,IAAI,CACd,KAAU,EACV,kBAA2C;QAE3C,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE,EAAE;YACtC,IAAI,KAAK,GAAY,KAAK,CAAA;YAC1B,IAAI,OAAO,kBAAkB,KAAK,UAAU,EAAE,CAAC;gBAC3C,MAAM,SAAS,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAA;gBAC1C,KAAK,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,CACtB,CAAC,UAAU,EAAE,EAAE,CACX,kBAAkB,CAAC,UAAU,CAAC,KAAK,SAAS,CACnD,CAAA;YACL,CAAC;iBAAM,IAAI,OAAO,kBAAkB,KAAK,QAAQ,EAAE,CAAC;gBAChD,KAAK,GAAG,CAAC,CAAC,WAAW,CAAC,IAAI,CACtB,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,kBAAkB,CAAC;oBAC9B,IAAI,CAAC,kBAAkB,CAAC,CAC/B,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,KAAK,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAA;YAC5C,CAAC;YAED,IAAI,CAAC,KAAK;gBAAE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAElC,OAAO,WAAW,CAAA;QACtB,CAAC,EAAE,EAAS,CAAC,CAAA;IACjB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,SAAS,CAAC,MAAW,EAAE,GAAG,OAAc;QAClD,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAClB,OAAO,MAAM,CAAA;QACjB,CAAC;QAED,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;QAClC,CAAC;QAED,OAAO,MAAM,CAAA;IACjB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,WAAW,CAAmB,MAAS;QACjD,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YAC1C,OAAO,MAAM,CAAA;QACjB,CAAC;QAED,OAAO,MAAM,CAAC,MAAM,CAChB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,EAC5C,MAAM,CACT,CAAA;IACL,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,WAAW,CAAC,GAAG,IAAW;QACpC,IAAI,CAAM,EAAE,CAAM,EAAE,SAAc,EAAE,UAAe,CAAA;QAEnD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACvB,OAAO,IAAI,CAAA,CAAC,mEAAmE;YAC/E,iDAAiD;QACrD,CAAC;QAED,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3C,SAAS,GAAG,EAAE,CAAA,CAAC,2BAA2B;YAC1C,UAAU,GAAG,EAAE,CAAA;YAEf,IACI,CAAC,IAAI,CAAC,eAAe,CACjB,SAAS,EACT,UAAU,EACV,SAAS,CAAC,CAAC,CAAC,EACZ,SAAS,CAAC,CAAC,CAAC,CACf,EACH,CAAC;gBACC,OAAO,KAAK,CAAA;YAChB,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,SAAS,CAAC,GAAkB,EAAE,IAAY;QACpD,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAChC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;QAC1B,CAAC;QACD,OAAO,GAAG,CAAA;IACd,CAAC;IAEM,MAAM,CAAC,+BAA+B,CAAC,GAAQ;QAClD,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;YACpB,IAAI,GAAG,CAAC,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,EAAE,CAAC;gBAC3C,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACrC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;gBACnB,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,+BAA+B,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;gBAClD,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAEM,MAAM,CAAC,2BAA2B,CAAC,KAAe;QACrD,MAAM,GAAG,GAAQ,EAAE,CAAA;QACnB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAC7B,IAAI,CAAC,KAAK,CAAC,MAAM;gBAAE,SAAQ;YAE3B,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;gBAC3C,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAA;YACtB,CAAC;YACD,IAAI,cAAc,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;YAClC,KAAK,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC;gBACxC,IAAI,GAAG,KAAK,CAAC;oBAAE,SAAQ;gBAEvB,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;oBACvB,cAAc,GAAG,cAAc,CAAC,IAAI,CAAC,CAAA;gBACzC,CAAC;qBAAM,IAAI,GAAG,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAClC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAA;oBACzB,cAAc,GAAG,IAAI,CAAA;gBACzB,CAAC;qBAAM,CAAC;oBACJ,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAA;oBACzB,cAAc,GAAG,cAAc,CAAC,IAAI,CAAC,CAAA;gBACzC,CAAC;YACL,CAAC;QACL,CAAC;QACD,IAAI,CAAC,+BAA+B,CAAC,GAAG,CAAC,CAAA;QACzC,OAAO,GAAG,CAAA;IACd,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,UAAU,CACpB,OAAkC,EAClC,QAAmC;QAEnC,IACI,OAAO,KAAK,SAAS;YACrB,OAAO,KAAK,IAAI;YAChB,QAAQ,KAAK,SAAS;YACtB,QAAQ,KAAK,IAAI;YAEjB,OAAO,KAAK,CAAA;QAEhB,wCAAwC;QACxC,IACI,CAAC,CAAC,OAAO,OAAO,CAAC,EAAE,KAAK,QAAQ;YAC5B,OAAO,QAAQ,CAAC,EAAE,KAAK,QAAQ,CAAC;YAChC,CAAC,OAAO,OAAO,CAAC,EAAE,KAAK,QAAQ;gBAC3B,OAAO,QAAQ,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,KAAK,CAAC,EACpC,CAAC;YACC,OAAO,OAAO,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAA;QACrC,CAAC;QAED,OAAO,QAAQ,CAAC,WAAW,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,SAAS,CAAC,KAAU;QAC9B,IAAI,OAAO,KAAK,KAAK,SAAS;YAAE,OAAO,KAAK,CAAA;QAE5C,IAAI,OAAO,KAAK,KAAK,QAAQ;YAAE,OAAO,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,GAAG,CAAA;QAEvE,IAAI,OAAO,KAAK,KAAK,QAAQ;YAAE,OAAO,KAAK,GAAG,CAAC,CAAA;QAE/C,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,SAAS,CAAC,IAAW,EAAE,MAAa;QAC9C,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE;YACzC,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAA;YAC9B,OAAO,MAAM,CAAA;QACjB,CAAC,EAAE,EAAmB,CAAC,CAAA;IAC3B,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,aAAa,CAAC,IAAW,EAAE,IAAW;QAChD,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM;YAAE,OAAO,KAAK,CAAA;QAC7C,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,EAAE;YAC1B,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAA;QACvC,CAAC,CAAC,CAAA;IACN,CAAC;IAEM,MAAM,CAAC,oBAAoB,CAAI,GAAG,KAAY;QACjD,MAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YAC1C,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,KAAK,IAAI,CAAC,CAAA;YAClE,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CACtB,UAAU,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAC3D,CAAA;QACL,CAAC,CAAC,CAAA;QACF,OAAO,CAAC,iBAAiB,CAAA;IAC7B,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,uBAAuB,CACjC,GAAW,EACX,UAAkB;QAElB,MAAM,SAAS,GAAG,GAAG,CAAC,KAAK,CACvB,IAAI,MAAM,CACN,IAAI,UAAU,8BAA8B,UAAU,aAAa,CACtE,CACJ,CAAA;QAED,IAAI,SAAS,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;YAC/B,MAAM,UAAU,GAAG,GAAG,CAAC,SAAS,CAC5B,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CACxC,CAAA;YAED,kBAAkB;YAClB,mDAAmD;YACnD,MAAM,KAAK,GAAG,UAAU,CAAA;YAExB;;;eAGG;YACH,IAAI,aAAa,GAAG,EAAE,CAAA;YACtB,IAAI,SAAS,GAAG,EAAE,CAAA;YAClB,MAAM,UAAU,GAAa,EAAE,CAAA;YAC/B,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC;gBAC1C,MAAM,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAA;gBACvB,QAAQ,IAAI,EAAE,CAAC;oBACX,KAAK,GAAG;wBACJ,IAAI,aAAa,IAAI,EAAE,EAAE,CAAC;4BACtB,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;4BAC1B,SAAS,GAAG,EAAE,CAAA;wBAClB,CAAC;6BAAM,CAAC;4BACJ,SAAS,IAAI,IAAI,CAAA;wBACrB,CAAC;wBACD,MAAK;oBACT,KAAK,GAAG;wBACJ,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;4BACxB,MAAM,eAAe,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI,CAAA;4BAC/C,IAAI,eAAe,EAAE,CAAC;gCAClB,6CAA6C;gCAC7C,gDAAgD;gCAChD,SAAS,IAAI,IAAI,CAAA;gCACjB,GAAG,IAAI,CAAC,CAAA,CAAC,uBAAuB;4BACpC,CAAC;iCAAM,CAAC;gCACJ,aAAa,GAAG,EAAE,CAAA;4BACtB,CAAC;wBACL,CAAC;6BAAM,CAAC;4BACJ,aAAa,GAAG,IAAI,CAAA;wBACxB,CAAC;wBACD,MAAK;oBACT,KAAK,GAAG;wBACJ,IAAI,aAAa,IAAI,EAAE,EAAE,CAAC;4BACtB,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;4BAC1B,OAAO,UAAU,CAAA;wBACrB,CAAC;6BAAM,CAAC;4BACJ,SAAS,IAAI,IAAI,CAAA;wBACrB,CAAC;wBACD,MAAK;oBACT;wBACI,IAAI,aAAa,IAAI,EAAE,EAAE,CAAC;4BACtB,SAAS,IAAI,IAAI,CAAA;wBACrB,CAAC;gBACT,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO,SAAS,CAAA;IACpB,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,qBAAqB,CAAC,QAAiB;QACjD,OAAO,CACH,QAAQ,KAAK,SAAS;YACtB,QAAQ,KAAK,IAAI;YACjB,QAAQ,KAAK,EAAE;YACf,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC;YAClD,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CACvE,CAAA;IACL,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,yBAAyB,CACnC,QAAiB;QAEjB,OAAO,CACH,OAAO,QAAQ,KAAK,QAAQ;YAC5B,OAAO,QAAQ,KAAK,QAAQ;YAC5B,QAAQ,YAAY,IAAI,CAC3B,CAAA;IACL,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,mBAAmB,CAC7B,QAAiB;QAEjB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1B,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE,CAC5B,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,CACxC,CAAA;QACL,CAAC;QAED,OAAO,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAA;IACnD,CAAC;IAED,4EAA4E;IAC5E,kBAAkB;IAClB,4EAA4E;IAEpE,MAAM,CAAC,eAAe,CAC1B,SAAc,EACd,UAAe,EACf,CAAM,EACN,CAAM;QAEN,IAAI,CAAC,CAAA;QAEL,0CAA0C;QAC1C,oCAAoC;QACpC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YAAE,OAAO,IAAI,CAAA;QAEnD,oCAAoC;QACpC,mDAAmD;QACnD,4DAA4D;QAC5D,IAAI,CAAC,KAAK,CAAC;YAAE,OAAO,IAAI,CAAA;QAExB,wEAAwE;QACxE,4BAA4B;QAC5B,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS;YAC9D,OAAO,KAAK,CAAA;QAEhB,8BAA8B;QAC9B,sDAAsD;QACtD,IACI,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,UAAU;YAC3B,OAAO,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC;YACnC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;YAEX,OAAO,IAAI,CAAA;QAEf,2DAA2D;QAC3D,2DAA2D;QAC3D,qDAAqD;QACrD,IACI,CAAC,OAAO,CAAC,KAAK,UAAU,IAAI,OAAO,CAAC,KAAK,UAAU,CAAC;YACpD,CAAC,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC;YACxC,CAAC,CAAC,YAAY,MAAM,IAAI,CAAC,YAAY,MAAM,CAAC;YAC5C,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,CAAC;YAChD,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,CAAC;YAEhD,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAA;QAExC,gDAAgD;QAChD,IAAI,CAAC,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,CAAC;YAAE,OAAO,KAAK,CAAA;QAEnE,IACI,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;YACzC,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;YAEzC,OAAO,KAAK,CAAA;QAEhB,IAAI,CAAC,CAAC,WAAW,KAAK,CAAC,CAAC,WAAW;YAAE,OAAO,KAAK,CAAA;QAEjD,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,SAAS;YAAE,OAAO,KAAK,CAAA;QAE7C,qCAAqC;QACrC,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACvD,OAAO,KAAK,CAAA;QAEhB,0DAA0D;QAC1D,4DAA4D;QAC5D,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACV,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC9C,OAAO,KAAK,CAAA;YAChB,CAAC;iBAAM,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACrC,OAAO,KAAK,CAAA;YAChB,CAAC;QACL,CAAC;QAED,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACV,IAAI,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC9C,OAAO,KAAK,CAAA;YAChB,CAAC;iBAAM,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACrC,OAAO,KAAK,CAAA;YAChB,CAAC;YAED,QAAQ,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClB,KAAK,QAAQ,CAAC;gBACd,KAAK,UAAU;oBACX,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;oBACjB,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;oBAElB,IACI,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAC1D,CAAC;wBACC,OAAO,KAAK,CAAA;oBAChB,CAAC;oBAED,SAAS,CAAC,GAAG,EAAE,CAAA;oBACf,UAAU,CAAC,GAAG,EAAE,CAAA;oBAChB,MAAK;gBAET;oBACI,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;wBAChB,OAAO,KAAK,CAAA;oBAChB,CAAC;oBACD,MAAK;YACb,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAA;IACf,CAAC;IAED,2EAA2E;IACnE,MAAM,CAAC,aAAa,CAAC,IAAS;QAClC,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACtC,OAAO,KAAK,CAAA;QAChB,CAAC;QAED,OAAO,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,KAAK,MAAM,CAAA;IAC3D,CAAC;IAEO,MAAM,CAAC,aAAa,CACxB,MAAW,EACX,GAAW,EACX,KAAU,EACV,IAAmB;QAEnB,yDAAyD;QACzD,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YAClB,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;YAC7B,OAAM;QACV,CAAC;QAED,IAAI,KAAK,YAAY,OAAO,EAAE,CAAC;YAC3B,0BAA0B;YAC1B,8EAA8E;YAC9E,kFAAkF;YAClF,qCAAqC;YACrC,OAAM;QACV,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACtD,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;YACnB,OAAM;QACV,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;YACf,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;QAChD,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAA;QAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;QACpC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IACtB,CAAC;IAEO,MAAM,CAAC,cAAc,CACzB,MAAW,EACX,GAAW,EACX,KAAU,EACV,IAAmB;QAEnB,yDAAyD;QACzD,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YAClB,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;YACjD,OAAM;QACV,CAAC;QAED,IAAI,KAAK,YAAY,OAAO,EAAE,CAAC;YAC3B,0BAA0B;YAC1B,8EAA8E;YAC9E,kFAAkF;YAClF,qCAAqC;YACrC,OAAM;QACV,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACtD,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,CAAA;YACvC,OAAM;QACV,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;YACf,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA;QACpE,CAAC;QAED,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAA;QAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;QACpC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IACtB,CAAC;IAEO,MAAM,CAAC,KAAK,CAChB,MAAW,EACX,MAAW,EACX,OAAsB,IAAI,GAAG,EAAE;QAE/B,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3D,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpC,IAAI,GAAG,KAAK,WAAW;oBAAE,SAAQ;gBACjC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAA;YACvD,CAAC;QACL,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACjD,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,EAAE,EAAE,CAAC;gBAC3C,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAA;YACtD,CAAC;QACL,CAAC;IACL,CAAC;CACJ;AAlkBD,4BAkkBC", "file": "OrmUtils.js", "sourcesContent": ["import { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport {\n    PrimitiveCriteria,\n    SinglePrimitiveCriteria,\n} from \"../common/PrimitiveCriteria\"\n\nexport class OrmUtils {\n    // -------------------------------------------------------------------------\n    // Public methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Chunks array into pieces.\n     */\n    public static chunk<T>(array: T[], size: number): T[][] {\n        return Array.from(Array(Math.ceil(array.length / size)), (_, i) => {\n            return array.slice(i * size, i * size + size)\n        })\n    }\n\n    public static splitClassesAndStrings<T>(\n        classesAndStrings: (string | T)[],\n    ): [T[], string[]] {\n        return [\n            classesAndStrings.filter(\n                (cls): cls is T => typeof cls !== \"string\",\n            ),\n            classesAndStrings.filter(\n                (str): str is string => typeof str === \"string\",\n            ),\n        ]\n    }\n\n    public static groupBy<T, R>(\n        array: T[],\n        propertyCallback: (item: T) => R,\n    ): { id: R; items: T[] }[] {\n        return array.reduce((groupedArray, value) => {\n            const key = propertyCallback(value)\n            let grouped = groupedArray.find((i) => i.id === key)\n            if (!grouped) {\n                grouped = { id: key, items: [] }\n                groupedArray.push(grouped)\n            }\n            grouped.items.push(value)\n            return groupedArray\n        }, [] as Array<{ id: R; items: T[] }>)\n    }\n\n    public static uniq<T>(array: T[], criteria?: (item: T) => any): T[]\n    public static uniq<T, K extends keyof T>(array: T[], property: K): T[]\n    public static uniq<T, K extends keyof T>(\n        array: T[],\n        criteriaOrProperty?: ((item: T) => any) | K,\n    ): T[] {\n        return array.reduce((uniqueArray, item) => {\n            let found: boolean = false\n            if (typeof criteriaOrProperty === \"function\") {\n                const itemValue = criteriaOrProperty(item)\n                found = !!uniqueArray.find(\n                    (uniqueItem) =>\n                        criteriaOrProperty(uniqueItem) === itemValue,\n                )\n            } else if (typeof criteriaOrProperty === \"string\") {\n                found = !!uniqueArray.find(\n                    (uniqueItem) =>\n                        uniqueItem[criteriaOrProperty] ===\n                        item[criteriaOrProperty],\n                )\n            } else {\n                found = uniqueArray.indexOf(item) !== -1\n            }\n\n            if (!found) uniqueArray.push(item)\n\n            return uniqueArray\n        }, [] as T[])\n    }\n\n    /**\n     * Deep Object.assign.\n     */\n    public static mergeDeep(target: any, ...sources: any[]): any {\n        if (!sources.length) {\n            return target\n        }\n\n        for (const source of sources) {\n            OrmUtils.merge(target, source)\n        }\n\n        return target\n    }\n\n    /**\n     * Creates a shallow copy of the object, without invoking the constructor\n     */\n    public static cloneObject<T extends object>(object: T): T {\n        if (object === null || object === undefined) {\n            return object\n        }\n\n        return Object.assign(\n            Object.create(Object.getPrototypeOf(object)),\n            object,\n        )\n    }\n\n    /**\n     * Deep compare objects.\n     *\n     * @see http://stackoverflow.com/a/1144249\n     */\n    public static deepCompare(...args: any[]): boolean {\n        let i: any, l: any, leftChain: any, rightChain: any\n\n        if (arguments.length < 1) {\n            return true // Die silently? Don't know how to handle such case, please help...\n            // throw \"Need two or more arguments to compare\";\n        }\n\n        for (i = 1, l = arguments.length; i < l; i++) {\n            leftChain = [] // Todo: this can be cached\n            rightChain = []\n\n            if (\n                !this.compare2Objects(\n                    leftChain,\n                    rightChain,\n                    arguments[0],\n                    arguments[i],\n                )\n            ) {\n                return false\n            }\n        }\n\n        return true\n    }\n\n    /**\n     * Gets deeper value of object.\n     */\n    public static deepValue(obj: ObjectLiteral, path: string) {\n        const segments = path.split(\".\")\n        for (let i = 0, len = segments.length; i < len; i++) {\n            obj = obj[segments[i]]\n        }\n        return obj\n    }\n\n    public static replaceEmptyObjectsWithBooleans(obj: any) {\n        for (const key in obj) {\n            if (obj[key] && typeof obj[key] === \"object\") {\n                if (Object.keys(obj[key]).length === 0) {\n                    obj[key] = true\n                } else {\n                    this.replaceEmptyObjectsWithBooleans(obj[key])\n                }\n            }\n        }\n    }\n\n    public static propertyPathsToTruthyObject(paths: string[]) {\n        const obj: any = {}\n        for (const path of paths) {\n            const props = path.split(\".\")\n            if (!props.length) continue\n\n            if (!obj[props[0]] || obj[props[0]] === true) {\n                obj[props[0]] = {}\n            }\n            let recursiveChild = obj[props[0]]\n            for (const [key, prop] of props.entries()) {\n                if (key === 0) continue\n\n                if (recursiveChild[prop]) {\n                    recursiveChild = recursiveChild[prop]\n                } else if (key === props.length - 1) {\n                    recursiveChild[prop] = {}\n                    recursiveChild = null\n                } else {\n                    recursiveChild[prop] = {}\n                    recursiveChild = recursiveChild[prop]\n                }\n            }\n        }\n        this.replaceEmptyObjectsWithBooleans(obj)\n        return obj\n    }\n\n    /**\n     * Check if two entity-id-maps are the same\n     */\n    public static compareIds(\n        firstId: ObjectLiteral | undefined,\n        secondId: ObjectLiteral | undefined,\n    ): boolean {\n        if (\n            firstId === undefined ||\n            firstId === null ||\n            secondId === undefined ||\n            secondId === null\n        )\n            return false\n\n        // Optimized version for the common case\n        if (\n            ((typeof firstId.id === \"string\" &&\n                typeof secondId.id === \"string\") ||\n                (typeof firstId.id === \"number\" &&\n                    typeof secondId.id === \"number\")) &&\n            Object.keys(firstId).length === 1 &&\n            Object.keys(secondId).length === 1\n        ) {\n            return firstId.id === secondId.id\n        }\n\n        return OrmUtils.deepCompare(firstId, secondId)\n    }\n\n    /**\n     * Transforms given value into boolean value.\n     */\n    public static toBoolean(value: any): boolean {\n        if (typeof value === \"boolean\") return value\n\n        if (typeof value === \"string\") return value === \"true\" || value === \"1\"\n\n        if (typeof value === \"number\") return value > 0\n\n        return false\n    }\n\n    /**\n     * Composes an object from the given array of keys and values.\n     */\n    public static zipObject(keys: any[], values: any[]): ObjectLiteral {\n        return keys.reduce((object, column, index) => {\n            object[column] = values[index]\n            return object\n        }, {} as ObjectLiteral)\n    }\n\n    /**\n     * Compares two arrays.\n     */\n    public static isArraysEqual(arr1: any[], arr2: any[]): boolean {\n        if (arr1.length !== arr2.length) return false\n        return arr1.every((element) => {\n            return arr2.indexOf(element) !== -1\n        })\n    }\n\n    public static areMutuallyExclusive<T>(...lists: T[][]): boolean {\n        const haveSharedObjects = lists.some((list) => {\n            const otherLists = lists.filter((otherList) => otherList !== list)\n            return list.some((item) =>\n                otherLists.some((otherList) => otherList.includes(item)),\n            )\n        })\n        return !haveSharedObjects\n    }\n\n    /**\n     * Parses the CHECK constraint on the specified column and returns\n     * all values allowed by the constraint or undefined if the constraint\n     * is not present.\n     */\n    public static parseSqlCheckExpression(\n        sql: string,\n        columnName: string,\n    ): string[] | undefined {\n        const enumMatch = sql.match(\n            new RegExp(\n                `\"${columnName}\" varchar CHECK\\\\s*\\\\(\\\\s*\"${columnName}\"\\\\s+IN\\\\s*`,\n            ),\n        )\n\n        if (enumMatch && enumMatch.index) {\n            const afterMatch = sql.substring(\n                enumMatch.index + enumMatch[0].length,\n            )\n\n            // This is an enum\n            // all enum values stored as a comma separated list\n            const chars = afterMatch\n\n            /**\n             * * When outside quotes: empty string\n             * * When inside single quotes: `'`\n             */\n            let currentQuotes = \"\"\n            let nextValue = \"\"\n            const enumValues: string[] = []\n            for (let idx = 0; idx < chars.length; idx++) {\n                const char = chars[idx]\n                switch (char) {\n                    case \",\":\n                        if (currentQuotes == \"\") {\n                            enumValues.push(nextValue)\n                            nextValue = \"\"\n                        } else {\n                            nextValue += char\n                        }\n                        break\n                    case \"'\":\n                        if (currentQuotes == char) {\n                            const isNextCharQuote = chars[idx + 1] === char\n                            if (isNextCharQuote) {\n                                // double quote in sql should be treated as a\n                                // single quote that's part of the quoted string\n                                nextValue += char\n                                idx += 1 // skip that next quote\n                            } else {\n                                currentQuotes = \"\"\n                            }\n                        } else {\n                            currentQuotes = char\n                        }\n                        break\n                    case \")\":\n                        if (currentQuotes == \"\") {\n                            enumValues.push(nextValue)\n                            return enumValues\n                        } else {\n                            nextValue += char\n                        }\n                        break\n                    default:\n                        if (currentQuotes != \"\") {\n                            nextValue += char\n                        }\n                }\n            }\n        }\n        return undefined\n    }\n\n    /**\n     * Checks if given criteria is null or empty.\n     */\n    public static isCriteriaNullOrEmpty(criteria: unknown): boolean {\n        return (\n            criteria === undefined ||\n            criteria === null ||\n            criteria === \"\" ||\n            (Array.isArray(criteria) && criteria.length === 0) ||\n            (this.isPlainObject(criteria) && Object.keys(criteria).length === 0)\n        )\n    }\n\n    /**\n     * Checks if given criteria is a primitive value.\n     * Primitive values are strings, numbers and dates.\n     */\n    public static isSinglePrimitiveCriteria(\n        criteria: unknown,\n    ): criteria is SinglePrimitiveCriteria {\n        return (\n            typeof criteria === \"string\" ||\n            typeof criteria === \"number\" ||\n            criteria instanceof Date\n        )\n    }\n\n    /**\n     * Checks if given criteria is a primitive value or an array of primitive values.\n     */\n    public static isPrimitiveCriteria(\n        criteria: unknown,\n    ): criteria is PrimitiveCriteria {\n        if (Array.isArray(criteria)) {\n            return criteria.every((value) =>\n                this.isSinglePrimitiveCriteria(value),\n            )\n        }\n\n        return this.isSinglePrimitiveCriteria(criteria)\n    }\n\n    // -------------------------------------------------------------------------\n    // Private methods\n    // -------------------------------------------------------------------------\n\n    private static compare2Objects(\n        leftChain: any,\n        rightChain: any,\n        x: any,\n        y: any,\n    ) {\n        let p\n\n        // remember that NaN === NaN returns false\n        // and isNaN(undefined) returns true\n        if (Number.isNaN(x) && Number.isNaN(y)) return true\n\n        // Compare primitives and functions.\n        // Check if both arguments link to the same object.\n        // Especially useful on the step where we compare prototypes\n        if (x === y) return true\n\n        // Unequal, but either is null or undefined (use case: jsonb comparison)\n        // PR #3776, todo: add tests\n        if (x === null || y === null || x === undefined || y === undefined)\n            return false\n\n        // Fix the buffer compare bug.\n        // See: https://github.com/typeorm/typeorm/issues/3654\n        if (\n            (typeof x.equals === \"function\" ||\n                typeof x.equals === \"function\") &&\n            x.equals(y)\n        )\n            return true\n\n        // Works in case when functions are created in constructor.\n        // Comparing dates is a common scenario. Another built-ins?\n        // We can even handle functions passed across iframes\n        if (\n            (typeof x === \"function\" && typeof y === \"function\") ||\n            (x instanceof Date && y instanceof Date) ||\n            (x instanceof RegExp && y instanceof RegExp) ||\n            (typeof x === \"string\" && typeof y === \"string\") ||\n            (typeof x === \"number\" && typeof y === \"number\")\n        )\n            return x.toString() === y.toString()\n\n        // At last checking prototypes as good as we can\n        if (!(typeof x === \"object\" && typeof y === \"object\")) return false\n\n        if (\n            Object.prototype.isPrototypeOf.call(x, y) ||\n            Object.prototype.isPrototypeOf.call(y, x)\n        )\n            return false\n\n        if (x.constructor !== y.constructor) return false\n\n        if (x.prototype !== y.prototype) return false\n\n        // Check for infinitive linking loops\n        if (leftChain.indexOf(x) > -1 || rightChain.indexOf(y) > -1)\n            return false\n\n        // Quick checking of one object being a subset of another.\n        // todo: cache the structure of arguments[0] for performance\n        for (p in y) {\n            if (y.hasOwnProperty(p) !== x.hasOwnProperty(p)) {\n                return false\n            } else if (typeof y[p] !== typeof x[p]) {\n                return false\n            }\n        }\n\n        for (p in x) {\n            if (y.hasOwnProperty(p) !== x.hasOwnProperty(p)) {\n                return false\n            } else if (typeof y[p] !== typeof x[p]) {\n                return false\n            }\n\n            switch (typeof x[p]) {\n                case \"object\":\n                case \"function\":\n                    leftChain.push(x)\n                    rightChain.push(y)\n\n                    if (\n                        !this.compare2Objects(leftChain, rightChain, x[p], y[p])\n                    ) {\n                        return false\n                    }\n\n                    leftChain.pop()\n                    rightChain.pop()\n                    break\n\n                default:\n                    if (x[p] !== y[p]) {\n                        return false\n                    }\n                    break\n            }\n        }\n\n        return true\n    }\n\n    // Checks if it's an object made by Object.create(null), {} or new Object()\n    private static isPlainObject(item: any) {\n        if (item === null || item === undefined) {\n            return false\n        }\n\n        return !item.constructor || item.constructor === Object\n    }\n\n    private static mergeArrayKey(\n        target: any,\n        key: number,\n        value: any,\n        memo: Map<any, any>,\n    ) {\n        // Have we seen this before?  Prevent infinite recursion.\n        if (memo.has(value)) {\n            target[key] = memo.get(value)\n            return\n        }\n\n        if (value instanceof Promise) {\n            // Skip promises entirely.\n            // This is a hold-over from the old code & is because we don't want to pull in\n            // the lazy fields.  Ideally we'd remove these promises via another function first\n            // but for now we have to do it here.\n            return\n        }\n\n        if (!this.isPlainObject(value) && !Array.isArray(value)) {\n            target[key] = value\n            return\n        }\n\n        if (!target[key]) {\n            target[key] = Array.isArray(value) ? [] : {}\n        }\n\n        memo.set(value, target[key])\n        this.merge(target[key], value, memo)\n        memo.delete(value)\n    }\n\n    private static mergeObjectKey(\n        target: any,\n        key: string,\n        value: any,\n        memo: Map<any, any>,\n    ) {\n        // Have we seen this before?  Prevent infinite recursion.\n        if (memo.has(value)) {\n            Object.assign(target, { [key]: memo.get(value) })\n            return\n        }\n\n        if (value instanceof Promise) {\n            // Skip promises entirely.\n            // This is a hold-over from the old code & is because we don't want to pull in\n            // the lazy fields.  Ideally we'd remove these promises via another function first\n            // but for now we have to do it here.\n            return\n        }\n\n        if (!this.isPlainObject(value) && !Array.isArray(value)) {\n            Object.assign(target, { [key]: value })\n            return\n        }\n\n        if (!target[key]) {\n            Object.assign(target, { [key]: Array.isArray(value) ? [] : {} })\n        }\n\n        memo.set(value, target[key])\n        this.merge(target[key], value, memo)\n        memo.delete(value)\n    }\n\n    private static merge(\n        target: any,\n        source: any,\n        memo: Map<any, any> = new Map(),\n    ): any {\n        if (this.isPlainObject(target) && this.isPlainObject(source)) {\n            for (const key of Object.keys(source)) {\n                if (key === \"__proto__\") continue\n                this.mergeObjectKey(target, key, source[key], memo)\n            }\n        }\n\n        if (Array.isArray(target) && Array.isArray(source)) {\n            for (let key = 0; key < source.length; key++) {\n                this.mergeArrayKey(target, key, source[key], memo)\n            }\n        }\n    }\n}\n"], "sourceRoot": ".."}