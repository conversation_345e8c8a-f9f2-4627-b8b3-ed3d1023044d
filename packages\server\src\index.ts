/**
 * RAGE MP Roleplay Server - Entry Point
 * 
 * This is the main entry point for the RAGE MP roleplay server.
 * It initializes the server and all its components.
 */

import 'reflect-metadata';
import { Server } from '@/core/Server';
import { Logger } from '@/utils/Logger';

// Import core modules
import { AuthModule } from '@/modules/auth/AuthModule';
import { PlayerModule } from '@/modules/player/PlayerModule';
import { VehicleModule } from '@/modules/vehicle/VehicleModule';
import { EconomyModule } from '@/modules/economy/EconomyModule';
import { JobModule } from '@/modules/job/JobModule';
import { FactionModule } from '@/modules/faction/FactionModule';
import { PropertyModule } from '@/modules/property/PropertyModule';
import { ChatModule } from '@/modules/chat/ChatModule';
import { AdminModule } from '@/modules/admin/AdminModule';

/**
 * Main server initialization function
 */
async function main(): Promise<void> {
  const logger = Logger.getInstance();
  
  try {
    logger.info('🎮 Initializing RAGE MP Roleplay Server...');
    
    // Get server instance
    const server = Server.getInstance();
    
    // Register core modules
    registerModules(server);
    
    // Initialize server
    await server.initialize();
    
    logger.info('🚀 Server started successfully!');
    logger.info('🎯 Ready to accept players');
    
  } catch (error) {
    logger.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

/**
 * Register all server modules
 */
function registerModules(server: Server): void {
  const logger = Logger.getInstance();
  
  logger.info('📦 Registering server modules...');
  
  // Core modules (order matters for dependencies)
  server.registerModule(new AuthModule());
  server.registerModule(new PlayerModule());
  server.registerModule(new VehicleModule());
  server.registerModule(new ChatModule());
  
  // Feature modules
  server.registerModule(new EconomyModule());
  server.registerModule(new JobModule());
  server.registerModule(new FactionModule());
  server.registerModule(new PropertyModule());
  
  // Admin module (should be last)
  server.registerModule(new AdminModule());
  
  logger.info('✅ All modules registered');
}

/**
 * Handle graceful shutdown
 */
process.on('SIGINT', async () => {
  const logger = Logger.getInstance();
  logger.info('🛑 Received SIGINT, shutting down gracefully...');
  
  try {
    const server = Server.getInstance();
    await server.shutdown();
    process.exit(0);
  } catch (error) {
    logger.error('❌ Error during shutdown:', error);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  const logger = Logger.getInstance();
  logger.info('🛑 Received SIGTERM, shutting down gracefully...');
  
  try {
    const server = Server.getInstance();
    await server.shutdown();
    process.exit(0);
  } catch (error) {
    logger.error('❌ Error during shutdown:', error);
    process.exit(1);
  }
});

// Start the server
main().catch((error) => {
  const logger = Logger.getInstance();
  logger.error('❌ Unhandled error in main:', error);
  process.exit(1);
});
