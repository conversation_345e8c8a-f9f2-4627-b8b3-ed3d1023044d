{"version": 3, "sources": ["../../src/driver/sap/SapDriver.ts"], "names": [], "mappings": ";;;AAAA,6BASc;AACd,+FAA2F;AAC3F,2DAAuD;AAEvD,gEAA4D;AAC5D,gFAA4E;AAC5E,8EAA0E;AAC1E,oDAAgD;AAChD,kDAA8C;AAM9C,qDAAiD;AAEjD,gDAA4C;AAE5C,gEAA4D;AAG5D;;;;GAIG;AACH,MAAa,SAAS;IAmMlB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB;QA/KlC;;;WAGG;QACH,WAAM,GAAU,EAAE,CAAA;QA0BlB;;WAEG;QACH,iBAAY,GAAY,KAAK,CAAA;QAE7B;;WAEG;QACH,gBAAW,GAAG,IAAI,CAAA;QAElB;;WAEG;QACH,uBAAkB,GAAG,QAAiB,CAAA;QAEtC;;;;;WAKG;QACH,uBAAkB,GAAiB;YAC/B,SAAS;YACT,UAAU;YACV,KAAK,EAAE,8BAA8B;YACrC,SAAS;YACT,QAAQ;YACR,cAAc;YACd,SAAS;YACT,KAAK,EAAE,8BAA8B;YACrC,MAAM;YACN,QAAQ;YACR,OAAO,EAAE,uCAAuC;YAChD,MAAM;YACN,MAAM;YACN,YAAY;YACZ,WAAW;YACX,SAAS;YACT,MAAM,EAAE,iEAAiE;YACzE,OAAO,EAAE,2BAA2B;YACpC,SAAS,EAAE,0CAA0C;YACrD,UAAU;YACV,MAAM,EAAE,4BAA4B;YACpC,UAAU,EAAE,4BAA4B;YACxC,WAAW,EAAE,4BAA4B;YACzC,OAAO;YACP,WAAW;YACX,MAAM;YACN,MAAM,EAAE,uCAAuC;YAC/C,OAAO;YACP,aAAa;YACb,UAAU;SACb,CAAA;QAED;;WAEG;QACH,yBAAoB,GAAiB,CAAC,YAAY,CAAC,CAAA;QAEnD;;WAEG;QACH,iBAAY,GAAiB,CAAC,aAAa,EAAE,UAAU,CAAC,CAAA;QAExD;;WAEG;QACH,0BAAqB,GAAiB;YAClC,SAAS;YACT,UAAU;YACV,UAAU;YACV,WAAW;YACX,WAAW;SACd,CAAA;QAED;;WAEG;QACH,6BAAwB,GAAiB,CAAC,SAAS,CAAC,CAAA;QAEpD;;WAEG;QACH,yBAAoB,GAAiB,CAAC,SAAS,CAAC,CAAA;QAEhD;;;WAGG;QACH,oBAAe,GAAsB;YACjC,UAAU,EAAE,WAAW;YACvB,iBAAiB,EAAE,mBAAmB;YACtC,UAAU,EAAE,WAAW;YACvB,iBAAiB,EAAE,mBAAmB;YACtC,UAAU,EAAE,WAAW;YACvB,kBAAkB,EAAE,IAAI;YACxB,OAAO,EAAE,SAAS;YAClB,SAAS,EAAE,SAAS;YACpB,WAAW,EAAE,SAAS;YACtB,aAAa,EAAE,UAAU;YACzB,kBAAkB,EAAE,QAAQ;YAC5B,OAAO,EAAE,SAAS;YAClB,eAAe,EAAE,UAAU;YAC3B,SAAS,EAAE,QAAQ;YACnB,aAAa,EAAE,SAAS;YACxB,UAAU,EAAE,gBAAuB;YACnC,WAAW,EAAE,OAAO;YACpB,YAAY,EAAE,UAAU;YACxB,gBAAgB,EAAE,UAAU;YAC5B,cAAc,EAAE,UAAU;YAC1B,aAAa,EAAE,UAAU;YACzB,YAAY,EAAE,UAAU;YACxB,aAAa,EAAE,gBAAuB;SACzC,CAAA;QAED;;;WAGG;QACH,qBAAgB,GAAqB;YACjC,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;YACnB,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;YACpB,OAAO,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE;YACxB,QAAQ,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE;YACzB,SAAS,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE;YAC1B,SAAS,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE;YAC1B,OAAO,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;SACvC,CAAA;QAED;;;WAGG;QACH,mBAAc,GAAG,GAAG,CAAA;QAEpB,oBAAe,GAAoB;YAC/B,OAAO,EAAE,IAAI;SAChB,CAAA;QAED,mBAAc,GAAG,WAAW,CAAA;QAOxB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAA+B,CAAA;QACzD,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAEvB,IAAI,CAAC,QAAQ,GAAG,yBAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAA;QACrE,IAAI,CAAC,MAAM,GAAG,yBAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAA;IACrE,CAAC;IAED,4EAA4E;IAC5E,6BAA6B;IAC7B,4EAA4E;IAE5E;;;;OAIG;IACH,KAAK,CAAC,OAAO;QACT,uBAAuB;QACvB,MAAM,QAAQ,GAAG;YACb,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;YAC3B,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI;YACvB,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;YAC/B,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;YAC/B,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK;SACxB,CAAA;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ;YAAE,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QACxE,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM;YAAE,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;QACrE,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO;YAAE,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA;QACjE,IAAI,IAAI,CAAC,OAAO,CAAC,sBAAsB;YACnC,QAAQ,CAAC,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAA;QACtE,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG;YAAE,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAA;QACrD,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI;YAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA;QACxD,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,CAAA;QAElD,eAAe;QACf,MAAM,OAAO,GAAQ;YACjB,GAAG,EACC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG;gBACtC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG;gBACvB,CAAC,CAAC,CAAC;YACX,GAAG,EACC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG;gBACtC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG;gBACvB,CAAC,CAAC,EAAE;SACf,CAAA;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa;YACpD,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAA;QAC3D,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB;YACzD,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAA;QACrE,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc;YACrD,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAA;QAC7D,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW;YAClD,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAA;QAEvD,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAA;QAElC,MAAM,gBAAgB,GAClB,OAAO,CAAC,gBAAgB;YACxB,CAAC,CAAC,KAAU,EAAE,EAAE,CACZ,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,kCAAkC,KAAK,EAAE,CAAC,CAAC,CAAA;QACtE,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAA;QAE1D,kBAAkB;QAClB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;QAEvD,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAA;QAEpD,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,WAAW,CAAC,qBAAqB,EAAE,CAAA;QACvE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAExB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,GAAG,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;QACtD,CAAC;QAED,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,YAAY;QACR,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAA;QACxB,IAAI,CAAC,IAAI,EAAE,CAAC;YACR,MAAM,IAAI,2BAAuB,CAAC,KAAK,CAAC,CAAA;QAC5C,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,SAAS,CAAA;QACvB,MAAM,IAAI,CAAC,KAAK,EAAE,CAAA;IACtB,CAAC;IAED;;OAEG;IACH,mBAAmB;QACf,OAAO,IAAI,uCAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAqB;QACnC,OAAO,IAAI,+BAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IACzC,CAAC;IAED;;;OAGG;IACH,yBAAyB,CACrB,GAAW,EACX,UAAyB,EACzB,gBAA+B;QAE/B,MAAM,iBAAiB,GAAU,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAC9D,CAAC,GAAG,EAAE,EAAE;YACJ,IAAI,gBAAgB,CAAC,GAAG,CAAC,YAAY,IAAI;gBACrC,OAAO,qBAAS,CAAC,yBAAyB,CACtC,gBAAgB,CAAC,GAAG,CAAC,EACrB,IAAI,CACP,CAAA;YAEL,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAA;QAChC,CAAC,CACJ,CAAA;QAED,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM;YAC9C,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;QAEnC,GAAG,GAAG,GAAG,CAAC,OAAO,CACb,6BAA6B,EAC7B,CAAC,IAAI,EAAE,OAAe,EAAE,GAAW,EAAU,EAAE;YAC3C,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAA;YACf,CAAC;YAED,MAAM,KAAK,GAAQ,UAAU,CAAC,GAAG,CAAC,CAAA;YAElC,IAAI,OAAO,EAAE,CAAC;gBACV,OAAO,KAAK;qBACP,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE;oBACZ,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;oBACzB,OAAO,IAAI,CAAC,eAAe,CACvB,GAAG,EACH,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAC/B,CAAA;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YACnB,CAAC;YAED,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;gBAC9B,OAAO,KAAK,EAAE,CAAA;YAClB,CAAC;YAED,IAAI,KAAK,YAAY,IAAI,EAAE,CAAC;gBACxB,OAAO,qBAAS,CAAC,yBAAyB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;YAC3D,CAAC;YAED,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC7B,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QAClE,CAAC,CACJ,CAAA,CAAC,kEAAkE;QACpE,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAkB;QACrB,OAAO,IAAI,UAAU,GAAG,CAAA;IAC5B,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,SAAiB,EAAE,MAAe;QAC7C,MAAM,SAAS,GAAG,CAAC,SAAS,CAAC,CAAA;QAE7B,IAAI,MAAM,EAAE,CAAC;YACT,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QAC7B,CAAC;QAED,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,cAAc,CACV,MAAgE;QAEhE,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAA;QACpC,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAA;QAEhC,IAAI,iCAAe,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,iCAAe,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YACpE,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAE/C,OAAO;gBACH,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,IAAI,cAAc;gBAC9D,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,YAAY;gBACtD,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,IAAI,iCAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAA;YAE9D,OAAO;gBACH,QAAQ,EACJ,MAAM,CAAC,kBAAkB;oBACzB,MAAM,CAAC,QAAQ;oBACf,cAAc;gBAClB,MAAM,EACF,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,MAAM,IAAI,YAAY;gBAC5D,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,IAAI,iCAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3C,2CAA2C;YAE3C,OAAO;gBACH,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,cAAc;gBAC3C,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,YAAY;gBACrC,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAE/B,OAAO;YACH,QAAQ,EAAE,cAAc;YACxB,MAAM,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,YAAY;YACjE,SAAS,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;SACpD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,KAAU,EAAE,cAA8B;QAC7D,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,+CAAsB,CAAC,WAAW,CACtC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QAEL,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;YAAE,OAAO,KAAK,CAAA;QAEvD,IAAI,cAAc,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClC,OAAO,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACjC,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,OAAO,qBAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QACjD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,OAAO,qBAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QACjD,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,WAAW;YACnC,cAAc,CAAC,IAAI,KAAK,IAAI,EAC9B,CAAC;YACC,OAAO,qBAAS,CAAC,yBAAyB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;QAC3D,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;YAC9C,OAAO,qBAAS,CAAC,yBAAyB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC5D,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YAChD,OAAO,qBAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;QAC/C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,OAAO,qBAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,OAAO,qBAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;aAAM,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;YAChC,OAAO,GAAG,EAAE,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,EAAO,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,CAAA;QAC9D,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,KAAU,EAAE,cAA8B;QAC3D,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;YACrC,OAAO,cAAc,CAAC,WAAW;gBAC7B,CAAC,CAAC,+CAAsB,CAAC,aAAa,CAChC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR;gBACH,CAAC,CAAC,KAAK,CAAA;QAEf,IAAI,cAAc,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;QAChC,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,WAAW;YACnC,cAAc,CAAC,IAAI,KAAK,YAAY;YACpC,cAAc,CAAC,IAAI,KAAK,IAAI,EAC9B,CAAC;YACC,KAAK,GAAG,qBAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAClD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,KAAK,GAAG,qBAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAClD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,KAAK,GAAG,qBAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YAChD,KAAK,GAAG,qBAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;QAChD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,KAAK,GAAG,qBAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC/C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,KAAK,GAAG,qBAAS,CAAC,kBAAkB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAA;QAC/D,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,8BAA8B;YAC9B,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;QACpD,CAAC;QAED,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,+CAAsB,CAAC,aAAa,CACxC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QAEL,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAKb;QACG,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;YAClD,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;YAC/B,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACjC,MAAM,MAAM,GACR,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ;gBAC7B,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;gBACzB,CAAC,CAAC,MAAM,CAAC,MAAM,CAAA;YAEvB,qHAAqH;YACrH,IAAI,MAAM,IAAI,MAAM,GAAG,EAAE,EAAE,CAAC;gBACxB,OAAO,MAAM,CAAA;YACjB,CAAC;YAED,OAAO,QAAQ,CAAA;QACnB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAChC,OAAO,UAAU,CAAA;QACrB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;YAC9B,OAAO,WAAW,CAAA;QACtB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACjC,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IAAK,MAAM,CAAC,IAAY,KAAK,MAAM,EAAE,CAAC;YACzC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAChC,OAAO,UAAU,CAAA;QACrB,CAAC;aAAM,IACH,MAAM,CAAC,IAAI,KAAK,cAAc;YAC9B,MAAM,CAAC,IAAI,KAAK,aAAa,EAC/B,CAAC;YACC,OAAO,OAAO,CAAA;QAClB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YACvC,OAAO,UAAU,CAAA;QACrB,CAAC;QAED,IAAI,yBAAW,CAAC,yBAAyB,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC;YACrD,uDAAuD;YACvD,IACI,MAAM,CAAC,IAAI,KAAK,SAAS;gBACzB,MAAM,CAAC,IAAI,KAAK,UAAU;gBAC1B,MAAM,CAAC,IAAI,KAAK,WAAW,EAC7B,CAAC;gBACC,OAAO,UAAU,CAAA;YACrB,CAAC;iBAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBAC1D,OAAO,OAAO,CAAA;YAClB,CAAC;iBAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBAChC,OAAO,OAAO,CAAA;YAClB,CAAC;QACL,CAAC;QAED,OAAQ,MAAM,CAAC,IAAe,IAAI,EAAE,CAAA;IACxC,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,cAA8B;QAC3C,MAAM,YAAY,GAAG,cAAc,CAAC,OAAO,CAAA;QAE3C,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;YACnC,OAAO,GAAG,YAAY,EAAE,CAAA;QAC5B,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,SAAS,EAAE,CAAC;YACpC,OAAO,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAA;QAC1C,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,UAAU,EAAE,CAAC;YACrC,OAAO,YAAY,EAAE,CAAA;QACzB,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;YACnC,OAAO,IAAI,YAAY,GAAG,CAAA;QAC9B,CAAC;QAED,IAAI,YAAY,KAAK,IAAI,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YACtD,OAAO,SAAS,CAAA;QACpB,CAAC;QAED,OAAO,GAAG,YAAY,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,MAAsB;QACpC,OAAO,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CACrC,CAAC,GAAG,EAAE,EAAE,CACJ,GAAG,CAAC,QAAQ;YACZ,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC;YACxB,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAChC,CAAA;IACL,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,MAAoC;QAChD,IAAI,MAAM,CAAC,MAAM;YAAE,OAAO,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAA;QAElD,IAAI,MAAM,CAAC,kBAAkB,KAAK,MAAM;YAAE,OAAO,IAAI,CAAA;QAErD,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,SAAS,CAAC;YACf,KAAK,UAAU,CAAC;YAChB,KAAK,WAAW,CAAC;YACjB,KAAK,MAAM;gBACP,OAAO,KAAK,CAAA;YAChB,KAAK,UAAU;gBACX,OAAO,KAAK,CAAA;YAChB,KAAK,WAAW;gBACZ,OAAO,KAAK,CAAA;QACpB,CAAC;QAED,OAAO,EAAE,CAAA;IACb,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAmB;QAC9B,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;QAEtB,2GAA2G;QAC3G,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/B,IAAI,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAA;QAC/C,CAAC;aAAM,IACH,MAAM,CAAC,SAAS,KAAK,IAAI;YACzB,MAAM,CAAC,SAAS,KAAK,SAAS;YAC9B,MAAM,CAAC,KAAK,KAAK,IAAI;YACrB,MAAM,CAAC,KAAK,KAAK,SAAS,EAC5B,CAAC;YACC,IAAI,IAAI,IAAI,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,KAAK,GAAG,CAAA;QACnD,CAAC;aAAM,IACH,MAAM,CAAC,SAAS,KAAK,IAAI;YACzB,MAAM,CAAC,SAAS,KAAK,SAAS,EAChC,CAAC;YACC,IAAI,IAAI,IAAI,MAAM,CAAC,SAAS,GAAG,CAAA;QACnC,CAAC;QAED,IAAI,MAAM,CAAC,OAAO;YAAE,IAAI,IAAI,QAAQ,CAAA;QAEpC,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,sBAAsB;QAClB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,IAAI,2BAAY,CAAC,sBAAsB,CAAC,CAAA;QAClD,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAA;IACtC,CAAC;IAED;;;;OAIG;IACH,qBAAqB;QACjB,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAA;IACxC,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,QAAwB,EAAE,YAA2B;QACpE,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CACjD,CAAC,GAAG,EAAE,eAAe,EAAE,EAAE;YACrB,IAAI,KAAU,CAAA;YACd,IACI,eAAe,CAAC,kBAAkB,KAAK,WAAW;gBAClD,YAAY,EACd,CAAC;gBACC,KAAK,GAAG,YAAY,CAAA;gBACpB,8DAA8D;gBAC9D,sEAAsE;gBACtE,uDAAuD;YAC3D,CAAC;YAED,OAAO,mBAAQ,CAAC,SAAS,CACrB,GAAG,EACH,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CACxC,CAAA;QACL,CAAC,EACD,EAAmB,CACtB,CAAA;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAA;IAC1E,CAAC;IAED;;;OAGG;IACH,kBAAkB,CACd,YAA2B,EAC3B,eAAiC;QAEjC,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,EAAE;YAC7C,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CACjC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,YAAY,CAChD,CAAA;YACD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACf,4DAA4D;gBAC5D,OAAO,KAAK,CAAA;YAChB,CAAC;YAED,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAA;YAE/D,OAAO,CACH,WAAW,CAAC,IAAI,KAAK,cAAc,CAAC,YAAY;gBAChD,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;gBACvD,CAAC,cAAc,CAAC,MAAM;oBAClB,WAAW,CAAC,MAAM;wBACd,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;gBAC7C,WAAW,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS;gBAClD,WAAW,CAAC,KAAK,KAAK,cAAc,CAAC,KAAK;gBAC1C,WAAW,CAAC,OAAO;oBACf,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC9C,CAAC,CAAC,WAAW,CAAC,WAAW;oBACrB,iBAAiB,KAAK,WAAW,CAAC,OAAO,CAAC,IAAI,kGAAkG;gBACpJ,WAAW,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS;gBAClD,WAAW,CAAC,UAAU,KAAK,cAAc,CAAC,UAAU;gBACpD,WAAW,CAAC,QAAQ;oBAChB,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;gBAC1C,CAAC,cAAc,CAAC,kBAAkB,KAAK,MAAM;oBACzC,WAAW,CAAC,WAAW,KAAK,cAAc,CAAC,WAAW,CAAC,CAC9D,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,uBAAuB;QACnB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,yBAAyB;QACrB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,6BAA6B;QACzB,OAAO,CAAC,yBAAW,CAAC,yBAAyB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;IAC9D,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,aAAqB,EAAE,KAAa;QAChD,OAAO,GAAG,CAAA;IACd,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,gBAAgB;QACtB,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,6BAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YACpE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACxB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,qCAAqC;YACrC,MAAM,IAAI,+DAA8B,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;QACpE,CAAC;QAED,IAAI,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBACjC,6BAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;gBACtC,IAAI,CAAC,YAAY,GAAG,6BAAa,CAAC,IAAI,CAClC,mCAAmC,CACtC,CAAA;YACL,CAAC;QACL,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,qCAAqC;YACrC,MAAM,IAAI,+DAA8B,CACpC,UAAU,EACV,kBAAkB,CACrB,CAAA;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACO,aAAa,CAAC,OAAgB;QACpC,IAAI,CAAC,OAAO;YAAE,OAAO,OAAO,CAAA;QAE5B,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA,CAAC,wCAAwC;QAEjF,OAAO,OAAO,CAAA;IAClB,CAAC;CACJ;AA70BD,8BA60BC", "file": "SapDriver.js", "sourcesContent": ["import {\n    ColumnType,\n    ConnectionIsNotSetError,\n    DataSource,\n    EntityMetadata,\n    ObjectLiteral,\n    Table,\n    TableColumn,\n    TableForeignKey,\n} from \"../..\"\nimport { DriverPackageNotInstalledError } from \"../../error/DriverPackageNotInstalledError\"\nimport { TypeORMError } from \"../../error/TypeORMError\"\nimport { ColumnMetadata } from \"../../metadata/ColumnMetadata\"\nimport { PlatformTools } from \"../../platform/PlatformTools\"\nimport { RdbmsSchemaBuilder } from \"../../schema-builder/RdbmsSchemaBuilder\"\nimport { ApplyValueTransformers } from \"../../util/ApplyValueTransformers\"\nimport { DateUtils } from \"../../util/DateUtils\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { Driver } from \"../Driver\"\nimport { CteCapabilities } from \"../types/CteCapabilities\"\nimport { DataTypeDefaults } from \"../types/DataTypeDefaults\"\nimport { MappedColumnTypes } from \"../types/MappedColumnTypes\"\nimport { SapConnectionOptions } from \"./SapConnectionOptions\"\nimport { SapQueryRunner } from \"./SapQueryRunner\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { DriverUtils } from \"../DriverUtils\"\nimport { View } from \"../../schema-builder/view/View\"\nimport { InstanceChecker } from \"../../util/InstanceChecker\"\nimport { UpsertType } from \"../types/UpsertType\"\n\n/**\n * Organizes communication with SAP Hana DBMS.\n *\n * todo: looks like there is no built in support for connection pooling, we need to figure out something\n */\nexport class SapDriver implements Driver {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection used by driver.\n     */\n    connection: DataSource\n\n    /**\n     * Hana Pool instance.\n     */\n    client: any\n\n    /**\n     * Hana Client streaming extension.\n     */\n    streamClient: any\n    /**\n     * Pool for master database.\n     */\n    master: any\n\n    /**\n     * Pool for slave databases.\n     * Used in replication.\n     */\n    slaves: any[] = []\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection options.\n     */\n    options: SapConnectionOptions\n\n    /**\n     * Version of SAP HANA. Requires a SQL query to the DB, so it is not always set\n     */\n    version?: string\n\n    /**\n     * Database name used to perform all write queries.\n     */\n    database?: string\n\n    /**\n     * Schema name used to perform all write queries.\n     */\n    schema?: string\n\n    /**\n     * Indicates if replication is enabled.\n     */\n    isReplicated: boolean = false\n\n    /**\n     * Indicates if tree tables are supported by this driver.\n     */\n    treeSupport = true\n\n    /**\n     * Represent transaction support by this driver\n     */\n    transactionSupport = \"simple\" as const\n\n    /**\n     * Gets list of supported column data types by a driver.\n     *\n     * @see https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/20a1569875191014b507cf392724b7eb.html\n     * @see https://help.sap.com/docs/hana-cloud-database/sap-hana-cloud-sap-hana-database-sql-reference-guide/data-types\n     */\n    supportedDataTypes: ColumnType[] = [\n        \"tinyint\",\n        \"smallint\",\n        \"int\", // typeorm alias for \"integer\"\n        \"integer\",\n        \"bigint\",\n        \"smalldecimal\",\n        \"decimal\",\n        \"dec\", // typeorm alias for \"decimal\"\n        \"real\",\n        \"double\",\n        \"float\", // database alias for \"real\" / \"double\"\n        \"date\",\n        \"time\",\n        \"seconddate\",\n        \"timestamp\",\n        \"boolean\",\n        \"char\", // not officially supported, in SAP HANA Cloud: alias for \"nchar\"\n        \"nchar\", // not officially supported\n        \"varchar\", // in SAP HANA Cloud: alias for \"nvarchar\"\n        \"nvarchar\",\n        \"text\", // removed in SAP HANA Cloud\n        \"alphanum\", // removed in SAP HANA Cloud\n        \"shorttext\", // removed in SAP HANA Cloud\n        \"array\",\n        \"varbinary\",\n        \"blob\",\n        \"clob\", // in SAP HANA Cloud: alias for \"nclob\"\n        \"nclob\",\n        \"st_geometry\",\n        \"st_point\",\n    ]\n\n    /**\n     * Returns type of upsert supported by driver if any\n     */\n    supportedUpsertTypes: UpsertType[] = [\"merge-into\"]\n\n    /**\n     * Gets list of spatial column data types.\n     */\n    spatialTypes: ColumnType[] = [\"st_geometry\", \"st_point\"]\n\n    /**\n     * Gets list of column data types that support length by a driver.\n     */\n    withLengthColumnTypes: ColumnType[] = [\n        \"varchar\",\n        \"nvarchar\",\n        \"alphanum\",\n        \"shorttext\",\n        \"varbinary\",\n    ]\n\n    /**\n     * Gets list of column data types that support precision by a driver.\n     */\n    withPrecisionColumnTypes: ColumnType[] = [\"decimal\"]\n\n    /**\n     * Gets list of column data types that support scale by a driver.\n     */\n    withScaleColumnTypes: ColumnType[] = [\"decimal\"]\n\n    /**\n     * Orm has special columns and we need to know what database column types should be for those types.\n     * Column types are driver dependant.\n     */\n    mappedDataTypes: MappedColumnTypes = {\n        createDate: \"timestamp\",\n        createDateDefault: \"CURRENT_TIMESTAMP\",\n        updateDate: \"timestamp\",\n        updateDateDefault: \"CURRENT_TIMESTAMP\",\n        deleteDate: \"timestamp\",\n        deleteDateNullable: true,\n        version: \"integer\",\n        treeLevel: \"integer\",\n        migrationId: \"integer\",\n        migrationName: \"nvarchar\",\n        migrationTimestamp: \"bigint\",\n        cacheId: \"integer\",\n        cacheIdentifier: \"nvarchar\",\n        cacheTime: \"bigint\",\n        cacheDuration: \"integer\",\n        cacheQuery: \"nvarchar(5000)\" as any,\n        cacheResult: \"nclob\",\n        metadataType: \"nvarchar\",\n        metadataDatabase: \"nvarchar\",\n        metadataSchema: \"nvarchar\",\n        metadataTable: \"nvarchar\",\n        metadataName: \"nvarchar\",\n        metadataValue: \"nvarchar(5000)\" as any,\n    }\n\n    /**\n     * Default values of length, precision and scale depends on column data type.\n     * Used in the cases when length/precision/scale is not specified by user.\n     */\n    dataTypeDefaults: DataTypeDefaults = {\n        char: { length: 1 },\n        nchar: { length: 1 },\n        varchar: { length: 255 },\n        nvarchar: { length: 255 },\n        shorttext: { length: 255 },\n        varbinary: { length: 255 },\n        decimal: { precision: 18, scale: 0 },\n    }\n\n    /**\n     * Max length allowed by SAP HANA for aliases (identifiers).\n     * @see https://help.sap.com/viewer/4fe29514fd584807ac9f2a04f6754767/2.0.03/en-US/20a760537519101497e3cfe07b348f3c.html\n     */\n    maxAliasLength = 128\n\n    cteCapabilities: CteCapabilities = {\n        enabled: true,\n    }\n\n    dummyTableName = `SYS.DUMMY`\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: DataSource) {\n        this.connection = connection\n        this.options = connection.options as SapConnectionOptions\n        this.loadDependencies()\n\n        this.database = DriverUtils.buildDriverOptions(this.options).database\n        this.schema = DriverUtils.buildDriverOptions(this.options).schema\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Performs connection to the database.\n     * Based on pooling options, it can either create connection immediately,\n     * either create a pool and create connection when needed.\n     */\n    async connect(): Promise<void> {\n        // HANA connection info\n        const dbParams = {\n            hostName: this.options.host,\n            port: this.options.port,\n            userName: this.options.username,\n            password: this.options.password,\n            ...this.options.extra,\n        }\n\n        if (this.options.database) dbParams.databaseName = this.options.database\n        if (this.options.schema) dbParams.currentSchema = this.options.schema\n        if (this.options.encrypt) dbParams.encrypt = this.options.encrypt\n        if (this.options.sslValidateCertificate)\n            dbParams.validateCertificate = this.options.sslValidateCertificate\n        if (this.options.key) dbParams.key = this.options.key\n        if (this.options.cert) dbParams.cert = this.options.cert\n        if (this.options.ca) dbParams.ca = this.options.ca\n\n        // pool options\n        const options: any = {\n            min:\n                this.options.pool && this.options.pool.min\n                    ? this.options.pool.min\n                    : 1,\n            max:\n                this.options.pool && this.options.pool.max\n                    ? this.options.pool.max\n                    : 10,\n        }\n\n        if (this.options.pool && this.options.pool.checkInterval)\n            options.checkInterval = this.options.pool.checkInterval\n        if (this.options.pool && this.options.pool.maxWaitingRequests)\n            options.maxWaitingRequests = this.options.pool.maxWaitingRequests\n        if (this.options.pool && this.options.pool.requestTimeout)\n            options.requestTimeout = this.options.pool.requestTimeout\n        if (this.options.pool && this.options.pool.idleTimeout)\n            options.idleTimeout = this.options.pool.idleTimeout\n\n        const { logger } = this.connection\n\n        const poolErrorHandler =\n            options.poolErrorHandler ||\n            ((error: any) =>\n                logger.log(\"warn\", `SAP Hana pool raised an error. ${error}`))\n        this.client.eventEmitter.on(\"poolError\", poolErrorHandler)\n\n        // create the pool\n        this.master = this.client.createPool(dbParams, options)\n\n        const queryRunner = this.createQueryRunner(\"master\")\n\n        const { version, database } = await queryRunner.getDatabaseAndVersion()\n        this.version = version\n        this.database = database\n\n        if (!this.schema) {\n            this.schema = await queryRunner.getCurrentSchema()\n        }\n\n        await queryRunner.release()\n    }\n\n    /**\n     * Makes any action after connection (e.g. create extensions in Postgres driver).\n     */\n    afterConnect(): Promise<void> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Closes connection with the database.\n     */\n    async disconnect(): Promise<void> {\n        const pool = this.master\n        if (!pool) {\n            throw new ConnectionIsNotSetError(\"sap\")\n        }\n\n        this.master = undefined\n        await pool.clear()\n    }\n\n    /**\n     * Creates a schema builder used to build and sync a schema.\n     */\n    createSchemaBuilder() {\n        return new RdbmsSchemaBuilder(this.connection)\n    }\n\n    /**\n     * Creates a query runner used to execute database queries.\n     */\n    createQueryRunner(mode: ReplicationMode) {\n        return new SapQueryRunner(this, mode)\n    }\n\n    /**\n     * Replaces parameters in the given sql with special escaping character\n     * and an array of parameter names to be passed to a query.\n     */\n    escapeQueryWithParameters(\n        sql: string,\n        parameters: ObjectLiteral,\n        nativeParameters: ObjectLiteral,\n    ): [string, any[]] {\n        const escapedParameters: any[] = Object.keys(nativeParameters).map(\n            (key) => {\n                if (nativeParameters[key] instanceof Date)\n                    return DateUtils.mixedDateToDatetimeString(\n                        nativeParameters[key],\n                        true,\n                    )\n\n                return nativeParameters[key]\n            },\n        )\n\n        if (!parameters || !Object.keys(parameters).length)\n            return [sql, escapedParameters]\n\n        sql = sql.replace(\n            /:(\\.\\.\\.)?([A-Za-z0-9_.]+)/g,\n            (full, isArray: string, key: string): string => {\n                if (!parameters.hasOwnProperty(key)) {\n                    return full\n                }\n\n                const value: any = parameters[key]\n\n                if (isArray) {\n                    return value\n                        .map((v: any) => {\n                            escapedParameters.push(v)\n                            return this.createParameter(\n                                key,\n                                escapedParameters.length - 1,\n                            )\n                        })\n                        .join(\", \")\n                }\n\n                if (typeof value === \"function\") {\n                    return value()\n                }\n\n                if (value instanceof Date) {\n                    return DateUtils.mixedDateToDatetimeString(value, true)\n                }\n\n                escapedParameters.push(value)\n                return this.createParameter(key, escapedParameters.length - 1)\n            },\n        ) // todo: make replace only in value statements, otherwise problems\n        return [sql, escapedParameters]\n    }\n\n    /**\n     * Escapes a column name.\n     */\n    escape(columnName: string): string {\n        return `\"${columnName}\"`\n    }\n\n    /**\n     * Build full table name with schema name and table name.\n     * E.g. myDB.mySchema.myTable\n     */\n    buildTableName(tableName: string, schema?: string): string {\n        const tablePath = [tableName]\n\n        if (schema) {\n            tablePath.unshift(schema)\n        }\n\n        return tablePath.join(\".\")\n    }\n\n    /**\n     * Parse a target table name or other types and return a normalized table definition.\n     */\n    parseTableName(\n        target: EntityMetadata | Table | View | TableForeignKey | string,\n    ): { database?: string; schema?: string; tableName: string } {\n        const driverDatabase = this.database\n        const driverSchema = this.schema\n\n        if (InstanceChecker.isTable(target) || InstanceChecker.isView(target)) {\n            const parsed = this.parseTableName(target.name)\n\n            return {\n                database: target.database || parsed.database || driverDatabase,\n                schema: target.schema || parsed.schema || driverSchema,\n                tableName: parsed.tableName,\n            }\n        }\n\n        if (InstanceChecker.isTableForeignKey(target)) {\n            const parsed = this.parseTableName(target.referencedTableName)\n\n            return {\n                database:\n                    target.referencedDatabase ||\n                    parsed.database ||\n                    driverDatabase,\n                schema:\n                    target.referencedSchema || parsed.schema || driverSchema,\n                tableName: parsed.tableName,\n            }\n        }\n\n        if (InstanceChecker.isEntityMetadata(target)) {\n            // EntityMetadata tableName is never a path\n\n            return {\n                database: target.database || driverDatabase,\n                schema: target.schema || driverSchema,\n                tableName: target.tableName,\n            }\n        }\n\n        const parts = target.split(\".\")\n\n        return {\n            database: driverDatabase,\n            schema: (parts.length > 1 ? parts[0] : undefined) || driverSchema,\n            tableName: parts.length > 1 ? parts[1] : parts[0],\n        }\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type and metadata.\n     */\n    preparePersistentValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformTo(\n                columnMetadata.transformer,\n                value,\n            )\n\n        if (value === null || value === undefined) return value\n\n        if (columnMetadata.type === Boolean) {\n            return value === true ? 1 : 0\n        } else if (columnMetadata.type === \"date\") {\n            return DateUtils.mixedDateToDateString(value)\n        } else if (columnMetadata.type === \"time\") {\n            return DateUtils.mixedDateToTimeString(value)\n        } else if (\n            columnMetadata.type === \"timestamp\" ||\n            columnMetadata.type === Date\n        ) {\n            return DateUtils.mixedDateToDatetimeString(value, true)\n        } else if (columnMetadata.type === \"seconddate\") {\n            return DateUtils.mixedDateToDatetimeString(value, false)\n        } else if (columnMetadata.type === \"simple-array\") {\n            return DateUtils.simpleArrayToString(value)\n        } else if (columnMetadata.type === \"simple-json\") {\n            return DateUtils.simpleJsonToString(value)\n        } else if (columnMetadata.type === \"simple-enum\") {\n            return DateUtils.simpleEnumToString(value)\n        } else if (columnMetadata.isArray) {\n            return () => `ARRAY(${value.map((it: any) => `'${it}'`)})`\n        }\n\n        return value\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type or metadata.\n     */\n    prepareHydratedValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (value === null || value === undefined)\n            return columnMetadata.transformer\n                ? ApplyValueTransformers.transformFrom(\n                      columnMetadata.transformer,\n                      value,\n                  )\n                : value\n\n        if (columnMetadata.type === Boolean) {\n            value = value ? true : false\n        } else if (\n            columnMetadata.type === \"timestamp\" ||\n            columnMetadata.type === \"seconddate\" ||\n            columnMetadata.type === Date\n        ) {\n            value = DateUtils.normalizeHydratedDate(value)\n        } else if (columnMetadata.type === \"date\") {\n            value = DateUtils.mixedDateToDateString(value)\n        } else if (columnMetadata.type === \"time\") {\n            value = DateUtils.mixedTimeToString(value)\n        } else if (columnMetadata.type === \"simple-array\") {\n            value = DateUtils.stringToSimpleArray(value)\n        } else if (columnMetadata.type === \"simple-json\") {\n            value = DateUtils.stringToSimpleJson(value)\n        } else if (columnMetadata.type === \"simple-enum\") {\n            value = DateUtils.stringToSimpleEnum(value, columnMetadata)\n        } else if (columnMetadata.type === Number) {\n            // convert to number if number\n            value = !isNaN(+value) ? parseInt(value) : value\n        }\n\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformFrom(\n                columnMetadata.transformer,\n                value,\n            )\n\n        return value\n    }\n\n    /**\n     * Creates a database type from a given column metadata.\n     */\n    normalizeType(column: {\n        type?: ColumnType\n        length?: number | string\n        precision?: number | null\n        scale?: number\n    }): string {\n        if (column.type === Number || column.type === \"int\") {\n            return \"integer\"\n        } else if (column.type === \"dec\") {\n            return \"decimal\"\n        } else if (column.type === \"float\") {\n            const length =\n                typeof column.length === \"string\"\n                    ? parseInt(column.length)\n                    : column.length\n\n            // https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/4ee2f261e9c44003807d08ccc2e249ac.html\n            if (length && length < 25) {\n                return \"real\"\n            }\n\n            return \"double\"\n        } else if (column.type === String) {\n            return \"nvarchar\"\n        } else if (column.type === Date) {\n            return \"timestamp\"\n        } else if (column.type === Boolean) {\n            return \"boolean\"\n        } else if ((column.type as any) === Buffer) {\n            return \"blob\"\n        } else if (column.type === \"uuid\") {\n            return \"nvarchar\"\n        } else if (\n            column.type === \"simple-array\" ||\n            column.type === \"simple-json\"\n        ) {\n            return \"nclob\"\n        } else if (column.type === \"simple-enum\") {\n            return \"nvarchar\"\n        }\n\n        if (DriverUtils.isReleaseVersionOrGreater(this, \"4.0\")) {\n            // SAP HANA Cloud deprecated / removed these data types\n            if (\n                column.type === \"varchar\" ||\n                column.type === \"alphanum\" ||\n                column.type === \"shorttext\"\n            ) {\n                return \"nvarchar\"\n            } else if (column.type === \"text\" || column.type === \"clob\") {\n                return \"nclob\"\n            } else if (column.type === \"char\") {\n                return \"nchar\"\n            }\n        }\n\n        return (column.type as string) || \"\"\n    }\n\n    /**\n     * Normalizes \"default\" value of the column.\n     */\n    normalizeDefault(columnMetadata: ColumnMetadata): string | undefined {\n        const defaultValue = columnMetadata.default\n\n        if (typeof defaultValue === \"number\") {\n            return `${defaultValue}`\n        }\n\n        if (typeof defaultValue === \"boolean\") {\n            return defaultValue ? \"true\" : \"false\"\n        }\n\n        if (typeof defaultValue === \"function\") {\n            return defaultValue()\n        }\n\n        if (typeof defaultValue === \"string\") {\n            return `'${defaultValue}'`\n        }\n\n        if (defaultValue === null || defaultValue === undefined) {\n            return undefined\n        }\n\n        return `${defaultValue}`\n    }\n\n    /**\n     * Normalizes \"isUnique\" value of the column.\n     */\n    normalizeIsUnique(column: ColumnMetadata): boolean {\n        return column.entityMetadata.indices.some(\n            (idx) =>\n                idx.isUnique &&\n                idx.columns.length === 1 &&\n                idx.columns[0] === column,\n        )\n    }\n\n    /**\n     * Returns default column lengths, which is required on column creation.\n     */\n    getColumnLength(column: ColumnMetadata | TableColumn): string {\n        if (column.length) return column.length.toString()\n\n        if (column.generationStrategy === \"uuid\") return \"36\"\n\n        switch (column.type) {\n            case \"varchar\":\n            case \"nvarchar\":\n            case \"shorttext\":\n            case String:\n                return \"255\"\n            case \"alphanum\":\n                return \"127\"\n            case \"varbinary\":\n                return \"255\"\n        }\n\n        return \"\"\n    }\n\n    /**\n     * Creates column type definition including length, precision and scale\n     */\n    createFullType(column: TableColumn): string {\n        let type = column.type\n\n        // used 'getColumnLength()' method, because SqlServer sets `varchar` and `nvarchar` length to 1 by default.\n        if (this.getColumnLength(column)) {\n            type += `(${this.getColumnLength(column)})`\n        } else if (\n            column.precision !== null &&\n            column.precision !== undefined &&\n            column.scale !== null &&\n            column.scale !== undefined\n        ) {\n            type += `(${column.precision},${column.scale})`\n        } else if (\n            column.precision !== null &&\n            column.precision !== undefined\n        ) {\n            type += `(${column.precision})`\n        }\n\n        if (column.isArray) type += \" array\"\n\n        return type\n    }\n\n    /**\n     * Obtains a new database connection to a master server.\n     * Used for replication.\n     * If replication is not setup then returns default connection's database connection.\n     */\n    obtainMasterConnection(): Promise<any> {\n        if (!this.master) {\n            throw new TypeORMError(\"Driver not Connected\")\n        }\n\n        return this.master.getConnection()\n    }\n\n    /**\n     * Obtains a new database connection to a slave server.\n     * Used for replication.\n     * If replication is not setup then returns master (default) connection's database connection.\n     */\n    obtainSlaveConnection(): Promise<any> {\n        return this.obtainMasterConnection()\n    }\n\n    /**\n     * Creates generated map of values generated or returned by database after INSERT query.\n     */\n    createGeneratedMap(metadata: EntityMetadata, insertResult: ObjectLiteral) {\n        const generatedMap = metadata.generatedColumns.reduce(\n            (map, generatedColumn) => {\n                let value: any\n                if (\n                    generatedColumn.generationStrategy === \"increment\" &&\n                    insertResult\n                ) {\n                    value = insertResult\n                    // } else if (generatedColumn.generationStrategy === \"uuid\") {\n                    //     console.log(\"getting db value:\", generatedColumn.databaseName);\n                    //     value = generatedColumn.getEntityValue(uuidMap);\n                }\n\n                return OrmUtils.mergeDeep(\n                    map,\n                    generatedColumn.createValueMap(value),\n                )\n            },\n            {} as ObjectLiteral,\n        )\n\n        return Object.keys(generatedMap).length > 0 ? generatedMap : undefined\n    }\n\n    /**\n     * Differentiate columns of this table and columns from the given column metadatas columns\n     * and returns only changed.\n     */\n    findChangedColumns(\n        tableColumns: TableColumn[],\n        columnMetadatas: ColumnMetadata[],\n    ): ColumnMetadata[] {\n        return columnMetadatas.filter((columnMetadata) => {\n            const tableColumn = tableColumns.find(\n                (c) => c.name === columnMetadata.databaseName,\n            )\n            if (!tableColumn) {\n                // we don't need new columns, we only need exist and changed\n                return false\n            }\n\n            const normalizedDefault = this.normalizeDefault(columnMetadata)\n\n            return (\n                tableColumn.name !== columnMetadata.databaseName ||\n                tableColumn.type !== this.normalizeType(columnMetadata) ||\n                (columnMetadata.length &&\n                    tableColumn.length !==\n                        this.getColumnLength(columnMetadata)) ||\n                tableColumn.precision !== columnMetadata.precision ||\n                tableColumn.scale !== columnMetadata.scale ||\n                tableColumn.comment !==\n                    this.escapeComment(columnMetadata.comment) ||\n                (!tableColumn.isGenerated &&\n                    normalizedDefault !== tableColumn.default) || // we included check for generated here, because generated columns already can have default values\n                tableColumn.isPrimary !== columnMetadata.isPrimary ||\n                tableColumn.isNullable !== columnMetadata.isNullable ||\n                tableColumn.isUnique !==\n                    this.normalizeIsUnique(columnMetadata) ||\n                (columnMetadata.generationStrategy !== \"uuid\" &&\n                    tableColumn.isGenerated !== columnMetadata.isGenerated)\n            )\n        })\n    }\n\n    /**\n     * Returns true if driver supports RETURNING / OUTPUT statement.\n     */\n    isReturningSqlSupported(): boolean {\n        return false\n    }\n\n    /**\n     * Returns true if driver supports uuid values generation on its own.\n     */\n    isUUIDGenerationSupported(): boolean {\n        return false\n    }\n\n    /**\n     * Returns true if driver supports fulltext indices.\n     */\n    isFullTextColumnTypeSupported(): boolean {\n        return !DriverUtils.isReleaseVersionOrGreater(this, \"4.0\")\n    }\n\n    /**\n     * Creates an escaped parameter.\n     */\n    createParameter(parameterName: string, index: number): string {\n        return \"?\"\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * If driver dependency is not given explicitly, then try to load it via \"require\".\n     */\n    protected loadDependencies(): void {\n        try {\n            const client = this.options.driver || PlatformTools.load(\"hdb-pool\")\n            this.client = client\n        } catch (e) {\n            // todo: better error for browser env\n            throw new DriverPackageNotInstalledError(\"SAP Hana\", \"hdb-pool\")\n        }\n\n        try {\n            if (!this.options.hanaClientDriver) {\n                PlatformTools.load(\"@sap/hana-client\")\n                this.streamClient = PlatformTools.load(\n                    \"@sap/hana-client/extension/Stream\",\n                )\n            }\n        } catch (e) {\n            // todo: better error for browser env\n            throw new DriverPackageNotInstalledError(\n                \"SAP Hana\",\n                \"@sap/hana-client\",\n            )\n        }\n    }\n\n    /**\n     * Escapes a given comment.\n     */\n    protected escapeComment(comment?: string) {\n        if (!comment) return comment\n\n        comment = comment.replace(/\\u0000/g, \"\") // Null bytes aren't allowed in comments\n\n        return comment\n    }\n}\n"], "sourceRoot": "../.."}