import { EventEmitter } from 'events';
import { Logger } from '@/utils/Logger';
import { DatabaseManager } from '@/database/DatabaseManager';
import { ModuleManager } from '@/core/ModuleManager';
import { EventManager } from '@/events/EventManager';
import { PlayerManager } from '@/core/PlayerManager';
import { VehicleManager } from '@/core/VehicleManager';
import { ConfigManager } from '@/utils/ConfigManager';
import { IModule } from '@/types';

/**
 * Main Server class that orchestrates all server components
 * Follows Singleton pattern to ensure single instance
 */
export class Server extends EventEmitter {
  private static instance: Server;
  private readonly logger: Logger;
  private readonly configManager: ConfigManager;
  private readonly databaseManager: DatabaseManager;
  private readonly moduleManager: ModuleManager;
  private readonly eventManager: EventManager;
  private readonly playerManager: PlayerManager;
  private readonly vehicleManager: VehicleManager;
  
  private isInitialized = false;
  private isShuttingDown = false;

  private constructor() {
    super();
    
    // Initialize core components
    this.logger = Logger.getInstance();
    this.configManager = ConfigManager.getInstance();
    this.databaseManager = DatabaseManager.getInstance();
    this.moduleManager = ModuleManager.getInstance();
    this.eventManager = EventManager.getInstance();
    this.playerManager = PlayerManager.getInstance();
    this.vehicleManager = VehicleManager.getInstance();

    this.setupErrorHandlers();
  }

  /**
   * Get singleton instance of Server
   */
  public static getInstance(): Server {
    if (!Server.instance) {
      Server.instance = new Server();
    }
    return Server.instance;
  }

  /**
   * Initialize the server and all its components
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      throw new Error('Server is already initialized');
    }

    try {
      this.logger.info('🚀 Starting RAGE MP Roleplay Server...');

      // Load configuration
      await this.configManager.load();
      this.logger.info('✅ Configuration loaded');

      // Initialize database
      await this.databaseManager.initialize();
      this.logger.info('✅ Database connected');

      // Initialize event system
      await this.eventManager.initialize();
      this.logger.info('✅ Event system initialized');

      // Initialize player management
      await this.playerManager.initialize();
      this.logger.info('✅ Player manager initialized');

      // Initialize vehicle management
      await this.vehicleManager.initialize();
      this.logger.info('✅ Vehicle manager initialized');

      // Load and initialize modules
      await this.moduleManager.loadModules();
      this.logger.info('✅ Modules loaded');

      // Setup RAGE MP event handlers
      this.setupRageEventHandlers();

      this.isInitialized = true;
      this.emit('server:initialized');
      
      this.logger.info('🎮 RAGE MP Roleplay Server is ready!');
      this.logger.info(`📊 Server Statistics:`);
      this.logger.info(`   - Modules loaded: ${this.moduleManager.getLoadedModules().length}`);
      this.logger.info(`   - Event handlers: ${this.eventManager.getHandlerCount()}`);
      this.logger.info(`   - Max players: ${mp.config.maxplayers}`);

    } catch (error) {
      this.logger.error('❌ Failed to initialize server:', error);
      throw error;
    }
  }

  /**
   * Gracefully shutdown the server
   */
  public async shutdown(): Promise<void> {
    if (this.isShuttingDown) {
      return;
    }

    this.isShuttingDown = true;
    this.logger.info('🛑 Shutting down server...');

    try {
      // Shutdown modules
      await this.moduleManager.shutdown();
      this.logger.info('✅ Modules shutdown');

      // Shutdown managers
      await this.vehicleManager.shutdown();
      await this.playerManager.shutdown();
      await this.eventManager.shutdown();
      this.logger.info('✅ Managers shutdown');

      // Close database connection
      await this.databaseManager.shutdown();
      this.logger.info('✅ Database disconnected');

      this.emit('server:shutdown');
      this.logger.info('👋 Server shutdown complete');

    } catch (error) {
      this.logger.error('❌ Error during shutdown:', error);
      throw error;
    }
  }

  /**
   * Register a module with the server
   */
  public registerModule(module: IModule): void {
    this.moduleManager.registerModule(module);
  }

  /**
   * Get server statistics
   */
  public getStats(): Record<string, unknown> {
    return {
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      playersOnline: mp.players.length,
      vehiclesSpawned: mp.vehicles.length,
      modulesLoaded: this.moduleManager.getLoadedModules().length,
      eventHandlers: this.eventManager.getHandlerCount(),
      isInitialized: this.isInitialized,
    };
  }

  /**
   * Setup error handlers for uncaught exceptions
   */
  private setupErrorHandlers(): void {
    process.on('uncaughtException', (error: Error) => {
      this.logger.error('Uncaught Exception:', error);
      this.shutdown().catch(() => {
        process.exit(1);
      });
    });

    process.on('unhandledRejection', (reason: unknown) => {
      this.logger.error('Unhandled Rejection:', reason);
    });

    process.on('SIGINT', () => {
      this.logger.info('Received SIGINT, shutting down gracefully...');
      this.shutdown().then(() => {
        process.exit(0);
      }).catch(() => {
        process.exit(1);
      });
    });

    process.on('SIGTERM', () => {
      this.logger.info('Received SIGTERM, shutting down gracefully...');
      this.shutdown().then(() => {
        process.exit(0);
      }).catch(() => {
        process.exit(1);
      });
    });
  }

  /**
   * Setup RAGE MP event handlers
   */
  private setupRageEventHandlers(): void {
    // Player events
    mp.events.add('playerJoin', (player: PlayerMp) => {
      this.playerManager.onPlayerJoin(player);
    });

    mp.events.add('playerQuit', (player: PlayerMp, exitType: string, reason: string) => {
      this.playerManager.onPlayerQuit(player, exitType, reason);
    });

    mp.events.add('playerChat', (player: PlayerMp, text: string) => {
      this.playerManager.onPlayerChat(player, text);
    });

    mp.events.add('playerCommand', (player: PlayerMp, command: string) => {
      this.playerManager.onPlayerCommand(player, command);
    });

    mp.events.add('playerDeath', (player: PlayerMp, reason: number, killer: PlayerMp) => {
      this.playerManager.onPlayerDeath(player, reason, killer);
    });

    mp.events.add('playerSpawn', (player: PlayerMp) => {
      this.playerManager.onPlayerSpawn(player);
    });

    // Vehicle events
    mp.events.add('vehicleDestroy', (vehicle: VehicleMp) => {
      this.vehicleManager.onVehicleDestroy(vehicle);
    });

    mp.events.add('playerEnterVehicle', (player: PlayerMp, vehicle: VehicleMp, seat: number) => {
      this.vehicleManager.onPlayerEnterVehicle(player, vehicle, seat);
    });

    mp.events.add('playerExitVehicle', (player: PlayerMp, vehicle: VehicleMp) => {
      this.vehicleManager.onPlayerExitVehicle(player, vehicle);
    });

    this.logger.info('✅ RAGE MP event handlers registered');
  }

  // Getters for accessing managers
  public get database(): DatabaseManager {
    return this.databaseManager;
  }

  public get modules(): ModuleManager {
    return this.moduleManager;
  }

  public get events(): EventManager {
    return this.eventManager;
  }

  public get players(): PlayerManager {
    return this.playerManager;
  }

  public get vehicles(): VehicleManager {
    return this.vehicleManager;
  }

  public get config(): ConfigManager {
    return this.configManager;
  }
}
