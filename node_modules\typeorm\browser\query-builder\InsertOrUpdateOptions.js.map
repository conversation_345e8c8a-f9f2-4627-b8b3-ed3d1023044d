{"version": 3, "sources": ["../browser/src/query-builder/InsertOrUpdateOptions.ts"], "names": [], "mappings": "", "file": "InsertOrUpdateOptions.js", "sourcesContent": ["import { UpsertType } from \"../driver/types/UpsertType\"\nimport { Brackets } from \"./Brackets\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\n\nexport type InsertOrUpdateOptions = {\n    /**\n     * If true, postg<PERSON> will skip the update if no values would be changed (reduces writes)\n     */\n    skipUpdateIfNoValuesChanged?: boolean\n    /**\n     * If included, postg<PERSON> will apply the index predicate to a conflict target (partial index)\n     */\n    indexPredicate?: string\n    upsertType?: UpsertType\n    overwriteCondition?: {\n        where: string | Brackets | ObjectLiteral | ObjectLiteral[]\n        parameters?: ObjectLiteral\n    }\n}\n"], "sourceRoot": ".."}