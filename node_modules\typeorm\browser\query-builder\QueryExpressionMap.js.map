{"version": 3, "sources": ["../browser/src/query-builder/QueryExpressionMap.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAA;AAG/B,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAA;AAG/C,OAAO,EAAE,mBAAmB,EAAE,MAAM,mCAAmC,CAAA;AACvE,OAAO,EAAE,sBAAsB,EAAE,MAAM,yCAAyC,CAAA;AAOhF,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAA;AAKvC;;GAEG;AACH,MAAM,OAAO,kBAAkB;IA4U3B,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAsB,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;QA/U5C,4EAA4E;QAC5E,oBAAoB;QACpB,4EAA4E;QAE5E;;WAEG;QACH,yBAAoB,GAAqB,MAAM,CAAA;QAE/C;;WAEG;QACH,gBAAW,GAAY,KAAK,CAAA;QAO5B;;WAEG;QACH,YAAO,GAAY,EAAE,CAAA;QAErB;;WAEG;QACH,cAAS,GAOS,QAAQ,CAAA;QAE1B;;WAEG;QACH,YAAO,GAAkB,EAAE,CAAA;QAE3B;;WAEG;QACH,qBAAgB,GAAW,CAAC,CAAA;QAE5B;;WAEG;QACH,mBAAc,GAAY,KAAK,CAAA;QAE/B;;WAEG;QACH,qBAAgB,GAAa,EAAE,CAAA;QAkB/B;;WAEG;QACH,0BAAqB,GAAqB,EAAE,CAAA;QAE5C;;WAEG;QACH,eAAU,GAAW,EAAE,CAAA;QAEvB;;WAEG;QACH,aAAQ,GAAY,KAAK,CAAA;QAezB;;WAEG;QACH,mBAAc,GAAoB,EAAE,CAAA;QAEpC;;WAEG;QACH,yBAAoB,GAA0B,EAAE,CAAA;QAEhD;;WAEG;QACH,4BAAuB,GAA6B,EAAE,CAAA;QAEtD;;WAEG;QACH,WAAM,GAAkB,EAAE,CAAA;QAE1B;;WAEG;QACH,YAAO,GAA2D,EAAE,CAAA;QAEpE;;WAEG;QACH,aAAQ,GAAqB,EAAE,CAAA;QAE/B;;WAEG;QACH,aAAQ,GAAa,EAAE,CAAA;QA+DvB;;;WAGG;QACH,gBAAW,GAAY,KAAK,CAAA;QAE5B;;WAEG;QACH,eAAU,GAAkB,EAAE,CAAA;QAE9B;;;;WAIG;QACH,oBAAe,GAAY,IAAI,CAAA;QAE/B;;;;WAIG;QACH,2BAAsB,GAAY,KAAK,CAAA;QAEvC;;;WAGG;QACH,mCAA8B,GAAW,EAAE,CAAA;QAE3C;;WAEG;QACH,aAAQ,GAAY,KAAK,CAAA;QAEzB;;;;WAIG;QACH,8BAAyB,GAAY,IAAI,CAAA;QAoBzC;;WAEG;QACH,YAAO,GAA+B,EAAE,CAAA;QAaxC;;;WAGG;QACH,kBAAa,GAAa,EAAE,CAAA;QAE5B;;WAEG;QACH,kBAAa,GAAoB,EAAE,CAAA;QAEnC;;;WAGG;QACH,iBAAY,GAAY,IAAI,CAAA;QAE5B;;WAEG;QACH,kBAAa,GAAY,IAAI,CAAA;QAE7B;;WAEG;QACH,mBAAc,GAAY,KAAK,CAAA;QAQ/B;;;;WAIG;QACH,qBAAgB,GAAkB,EAAE,CAAA;QAOpC;;;;WAIG;QACH,qBAAgB,GAAqC,EAAE,CAAA;QAEvD,2BAAsB,GAIhB,EAAE,CAAA;QAOJ,IAAI,UAAU,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC;YAC1C,IAAI,CAAC,oBAAoB,GAAG,UAAU,CAAC,OAAO,CAAC,oBAAoB,CAAA;QACvE,CAAC;QAED,IAAI,CAAC,UAAU;YACV,UAAU,CAAC,OAAsC;gBAC9C,EAAE,iBAAiB,IAAI,KAAK,CAAA;IACxC,CAAC;IAED,4EAA4E;IAC5E,YAAY;IACZ,4EAA4E;IAE5E;;;OAGG;IACH,IAAI,WAAW;QACX,IACI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM;YAClC,IAAI,CAAC,SAAU,CAAC,WAAW;YAC3B,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC,EACrD,CAAC;YACC,MAAM,aAAa,GAAG,IAAI,CAAC,SAAU,CAAC,QAAQ,CAAC,OAAO,IAAI,EAAE,CAAA;YAC5D,OAAO,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,GAAG,EAAE,EAAE;gBACtD,OAAO,CAAC,IAAI,CAAC,SAAU,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,CAAA;gBAC9D,OAAO,OAAO,CAAA;YAClB,CAAC,EAAE,EAAsB,CAAC,CAAA;QAC9B,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAA;IACxB,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,YAAY,CAAC,KAAY;QACrB,6DAA6D;QAC7D,sBAAsB;QACtB,iEAAiE;QAEjE,qBAAqB;QACrB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;QAEtB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,OAOX;QACG,IAAI,SAAS,GAAG,OAAO,CAAC,IAAI,CAAA;QAC5B,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS;YAAE,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;QAClE,IAAI,CAAC,SAAS,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,UAAU;YAClD,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI,CAAA;QACnC,IAAI,CAAC,SAAS,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ;YAChD,SAAS,GAAG,OAAO,CAAC,MAAM,CAAA;QAE9B,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAA;QACzB,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;QACzB,IAAI,SAAS;YAAE,KAAK,CAAC,IAAI,GAAG,SAAS,CAAA;QACrC,IAAI,OAAO,CAAC,QAAQ;YAAE,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;QACvD,IAAI,OAAO,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,WAAW;YACpC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QAChE,IAAI,OAAO,CAAC,SAAS;YAAE,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;QAC1D,IAAI,OAAO,CAAC,QAAQ;YAAE,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;QAEvD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACxB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;;OAGG;IACH,eAAe,CAAC,SAAiB;QAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,CAAA;QACpE,IAAI,CAAC,KAAK;YACN,MAAM,IAAI,YAAY,CAClB,IAAI,SAAS,qDAAqD,CACrE,CAAA;QAEL,OAAO,KAAK,CAAA;IAChB,CAAC;IAED,2BAA2B,CACvB,eAAuB;QAEvB,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC5D,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAA;QAC7C,OAAO,KAAK,CAAC,QAAQ,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAA;IAClE,CAAC;IAED;;;;OAIG;IACH,IAAI,gBAAgB;QAChB,IAAI,CAAC,IAAI,CAAC,SAAS;YACf,MAAM,IAAI,YAAY,CAAC,uCAAuC,CAAC,CAAA,CAAC,uBAAuB;QAE3F,MAAM,gBAAgB,GAClB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,4BAA4B,CAChD,IAAI,CAAC,oBAAoB,CAC5B,CAAA;QACL,IAAI,CAAC,gBAAgB;YACjB,MAAM,IAAI,YAAY,CAClB,YAAY,IAAI,CAAC,oBAAoB,4BAA4B,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CACzF,CAAA,CAAC,uBAAuB;QAE7B,OAAO,gBAAgB,CAAA;IAC3B,CAAC;IAED;;;OAGG;IACH,KAAK;QACD,MAAM,GAAG,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QACnD,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;QAC9B,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,CAAA;QAClD,GAAG,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAA;QAC5C,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAA;QACxC,GAAG,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAA;QAC5C,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACnE,GAAG,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAA;QACpD,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;QAC9B,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;QAC9B,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;QAC9B,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAA;QAChC,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC5B,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC5B,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CACxC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAC3D,CAAA;QACD,GAAG,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CACpD,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,mBAAmB,CAAC,IAAI,EAAE,UAAU,CAAC,CAC5D,CAAA;QACD,GAAG,CAAC,uBAAuB,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAC1D,CAAC,aAAa,EAAE,EAAE,CAAC,IAAI,sBAAsB,CAAC,IAAI,EAAE,aAAa,CAAC,CACrE,CAAA;QACD,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,CAAC,CAAA;QACvD,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC,CAAC,CAAA;QAC3D,GAAG,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC/C,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,CAAA;QACtD,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;QACtB,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QACxB,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACpB,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;QACpB,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC5B,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC5B,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAA;QAClC,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAA;QAChC,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAA;QAClC,GAAG,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;QACnD,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAA;QAC1C,GAAG,CAAC,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAA;QACxD,GAAG,CAAC,8BAA8B,GAAG,IAAI,CAAC,8BAA8B,CAAA;QACxE,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC5B,GAAG,CAAC,yBAAyB,GAAG,IAAI,CAAC,yBAAyB,CAAA;QAC9D,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;QACtB,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC1B,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAA;QACtC,GAAG,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAA;QACpD,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAA;QAChB,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAA;QACtC,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAA;QACtC,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAA;QACpC,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAA;QACtC,GAAG,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAA;QACxC,GAAG,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAA;QAChC,GAAG,CAAC,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAA;QAC/D,GAAG,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC1B,GAAG,CAAC,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,CACxD,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YACb,KAAK,EAAE,UAAU,CAAC,KAAK;YACvB,YAAY,EACR,OAAO,UAAU,CAAC,YAAY,KAAK,QAAQ;gBACvC,CAAC,CAAC,UAAU,CAAC,YAAY;gBACzB,CAAC,CAAC,UAAU,CAAC,YAAY,CAAC,KAAK,EAAE;YACzC,OAAO,EAAE,UAAU,CAAC,OAAO;SAC9B,CAAC,CACL,CAAA;QACD,OAAO,GAAG,CAAA;IACd,CAAC;CACJ", "file": "QueryExpressionMap.js", "sourcesContent": ["import { <PERSON><PERSON> } from \"./Alias\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { OrderByCondition } from \"../find-options/OrderByCondition\"\nimport { JoinAttribute } from \"./JoinAttribute\"\nimport { QueryBuilder } from \"./QueryBuilder\"\nimport { QueryBuilderCteOptions } from \"./QueryBuilderCte\"\nimport { RelationIdAttribute } from \"./relation-id/RelationIdAttribute\"\nimport { RelationCountAttribute } from \"./relation-count/RelationCountAttribute\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { SelectQuery } from \"./SelectQuery\"\nimport { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport { RelationMetadata } from \"../metadata/RelationMetadata\"\nimport { SelectQueryBuilderOption } from \"./SelectQueryBuilderOption\"\nimport { TypeORMError } from \"../error\"\nimport { WhereClause } from \"./WhereClause\"\nimport { UpsertType } from \"../driver/types/UpsertType\"\nimport { CockroachConnectionOptions } from \"../driver/cockroachdb/CockroachConnectionOptions\"\n\n/**\n * Contains all properties of the QueryBuilder that needs to be build a final query.\n */\nexport class QueryExpressionMap {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Strategy to load relations.\n     */\n    relationLoadStrategy: \"join\" | \"query\" = \"join\"\n\n    /**\n     * Indicates if QueryBuilder used to select entities and not a raw results.\n     */\n    queryEntity: boolean = false\n\n    /**\n     * Main alias is a main selection object selected by QueryBuilder.\n     */\n    mainAlias?: Alias\n\n    /**\n     * All aliases (including main alias) used in the query.\n     */\n    aliases: Alias[] = []\n\n    /**\n     * Represents query type. QueryBuilder is able to build SELECT, UPDATE and DELETE queries.\n     */\n    queryType:\n        | \"select\"\n        | \"update\"\n        | \"delete\"\n        | \"insert\"\n        | \"relation\"\n        | \"soft-delete\"\n        | \"restore\" = \"select\"\n\n    /**\n     * Data needs to be SELECT-ed.\n     */\n    selects: SelectQuery[] = []\n\n    /**\n     * Max execution time in millisecond.\n     */\n    maxExecutionTime: number = 0\n\n    /**\n     * Whether SELECT is DISTINCT.\n     */\n    selectDistinct: boolean = false\n\n    /**\n     * SELECT DISTINCT ON query (postgres).\n     */\n    selectDistinctOn: string[] = []\n\n    /**\n     * FROM-s to be selected.\n     */\n    // froms: { target: string, alias: string }[] = [];\n\n    /**\n     * If update query was used, it needs \"update set\" - properties which will be updated by this query.\n     * If insert query was used, it needs \"insert set\" - values that needs to be inserted.\n     */\n    valuesSet?: ObjectLiteral | ObjectLiteral[]\n\n    /**\n     * Optional returning (or output) clause for insert, update or delete queries.\n     */\n    returning: string | string[]\n\n    /**\n     * Extra returning columns to be added to the returning statement if driver supports it.\n     */\n    extraReturningColumns: ColumnMetadata[] = []\n\n    /**\n     * Optional on conflict statement used in insertion query in postgres.\n     */\n    onConflict: string = \"\"\n\n    /**\n     * Optional on ignore statement used in insertion query in databases.\n     */\n    onIgnore: boolean = false\n\n    /**\n     * Optional on update statement used in insertion query in databases.\n     */\n    onUpdate: {\n        conflict?: string | string[]\n        columns?: string[]\n        overwrite?: string[]\n        skipUpdateIfNoValuesChanged?: boolean\n        indexPredicate?: string\n        upsertType?: UpsertType\n        overwriteCondition?: WhereClause[]\n    }\n\n    /**\n     * JOIN queries.\n     */\n    joinAttributes: JoinAttribute[] = []\n\n    /**\n     * RelationId queries.\n     */\n    relationIdAttributes: RelationIdAttribute[] = []\n\n    /**\n     * Relation count queries.\n     */\n    relationCountAttributes: RelationCountAttribute[] = []\n\n    /**\n     * WHERE queries.\n     */\n    wheres: WhereClause[] = []\n\n    /**\n     * HAVING queries.\n     */\n    havings: { type: \"simple\" | \"and\" | \"or\"; condition: string }[] = []\n\n    /**\n     * ORDER BY queries.\n     */\n    orderBys: OrderByCondition = {}\n\n    /**\n     * GROUP BY queries.\n     */\n    groupBys: string[] = []\n\n    /**\n     * LIMIT query.\n     */\n    limit?: number\n\n    /**\n     * OFFSET query.\n     */\n    offset?: number\n\n    /**\n     * Number of rows to skip of result using pagination.\n     */\n    skip?: number\n\n    /**\n     * Number of rows to take using pagination.\n     */\n    take?: number\n\n    /**\n     * Use certain index for the query.\n     *\n     * SELECT * FROM table_name USE INDEX (col1_index, col2_index) WHERE col1=1 AND col2=2 AND col3=3;\n     */\n    useIndex?: string\n\n    /**\n     * Locking mode.\n     */\n    lockMode?:\n        | \"optimistic\"\n        | \"pessimistic_read\"\n        | \"pessimistic_write\"\n        | \"dirty_read\"\n        /*\n            \"pessimistic_partial_write\" and \"pessimistic_write_or_fail\" are deprecated and\n            will be removed in a future version.\n\n            Use onLocked instead.\n         */\n        | \"pessimistic_partial_write\"\n        | \"pessimistic_write_or_fail\"\n        | \"for_no_key_update\"\n        | \"for_key_share\"\n\n    /**\n     * Current version of the entity, used for locking.\n     */\n    lockVersion?: number | Date\n\n    /**\n     * Tables to be specified in the \"FOR UPDATE OF\" clause, referred by their alias\n     */\n    lockTables?: string[]\n\n    /**\n     * Modify behavior when encountering locked rows. NOWAIT or SKIP LOCKED\n     */\n    onLocked?: \"nowait\" | \"skip_locked\"\n\n    /**\n     * Indicates if soft-deleted rows should be included in entity result.\n     * By default the soft-deleted rows are not included.\n     */\n    withDeleted: boolean = false\n\n    /**\n     * Parameters used to be escaped in final query.\n     */\n    parameters: ObjectLiteral = {}\n\n    /**\n     * Indicates if alias, table names and column names will be escaped by driver, or not.\n     *\n     * todo: rename to isQuotingDisabled, also think if it should be named \"escaping\"\n     */\n    disableEscaping: boolean = true\n\n    /**\n     * Indicates if virtual columns should be included in entity result.\n     *\n     * todo: what to do with it? is it properly used? what about persistence?\n     */\n    enableRelationIdValues: boolean = false\n\n    /**\n     * Extra where condition appended to the end of original where conditions with AND keyword.\n     * Original condition will be wrapped into brackets.\n     */\n    extraAppendedAndWhereCondition: string = \"\"\n\n    /**\n     * Indicates if query builder creates a subquery.\n     */\n    subQuery: boolean = false\n\n    /**\n     * Indicates if property names are prefixed with alias names during property replacement.\n     * By default this is enabled, however we need this because aliases are not supported in UPDATE and DELETE queries,\n     * but user can use them in WHERE expressions.\n     */\n    aliasNamePrefixingEnabled: boolean = true\n\n    /**\n     * Indicates if query result cache is enabled or not.\n     * It is undefined by default to avoid overriding the `alwaysEnabled` config\n     */\n    cache?: boolean\n\n    /**\n     * Time in milliseconds in which cache will expire.\n     * If not set then global caching time will be used.\n     */\n    cacheDuration: number\n\n    /**\n     * Cache id.\n     * Used to identifier your cache queries.\n     */\n    cacheId: string\n\n    /**\n     * Options that define QueryBuilder behaviour.\n     */\n    options: SelectQueryBuilderOption[] = []\n\n    /**\n     * Property path of relation to work with.\n     * Used in relational query builder.\n     */\n    relationPropertyPath: string\n\n    /**\n     * Entity (target) which relations will be updated.\n     */\n    of: any | any[]\n\n    /**\n     * List of columns where data should be inserted.\n     * Used in INSERT query.\n     */\n    insertColumns: string[] = []\n\n    /**\n     * Used if user wants to update or delete a specific entities.\n     */\n    whereEntities: ObjectLiteral[] = []\n\n    /**\n     * Indicates if entity must be updated after insertion / updation.\n     * This may produce extra query or use RETURNING / OUTPUT statement (depend on database).\n     */\n    updateEntity: boolean = true\n\n    /**\n     * Indicates if listeners and subscribers must be called before and after query execution.\n     */\n    callListeners: boolean = true\n\n    /**\n     * Indicates if query must be wrapped into transaction.\n     */\n    useTransaction: boolean = false\n\n    /**\n     * Indicates if query should be time travel query\n     * https://www.cockroachlabs.com/docs/stable/as-of-system-time.html\n     */\n    timeTravel?: boolean | string\n\n    /**\n     * Extra parameters.\n     *\n     * @deprecated Use standard parameters instead\n     */\n    nativeParameters: ObjectLiteral = {}\n\n    /**\n     * Query Comment to include extra information for debugging or other purposes.\n     */\n    comment?: string\n\n    /**\n     * Items from an entity that have been locally generated & are recorded here for later use.\n     * Examples include the UUID generation when the database does not natively support it.\n     * These are included in the entity index order.\n     */\n    locallyGenerated: { [key: number]: ObjectLiteral } = {}\n\n    commonTableExpressions: {\n        queryBuilder: QueryBuilder<any> | string\n        alias: string\n        options: QueryBuilderCteOptions\n    }[] = []\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(protected connection: DataSource) {\n        if (connection.options.relationLoadStrategy) {\n            this.relationLoadStrategy = connection.options.relationLoadStrategy\n        }\n\n        this.timeTravel =\n            (connection.options as CockroachConnectionOptions)\n                ?.timeTravelQueries || false\n    }\n\n    // -------------------------------------------------------------------------\n    // Accessors\n    // -------------------------------------------------------------------------\n\n    /**\n     * Get all ORDER BY queries - if order by is specified by user then it uses them,\n     * otherwise it uses default entity order by if it was set.\n     */\n    get allOrderBys() {\n        if (\n            !Object.keys(this.orderBys).length &&\n            this.mainAlias!.hasMetadata &&\n            this.options.indexOf(\"disable-global-order\") === -1\n        ) {\n            const entityOrderBy = this.mainAlias!.metadata.orderBy || {}\n            return Object.keys(entityOrderBy).reduce((orderBy, key) => {\n                orderBy[this.mainAlias!.name + \".\" + key] = entityOrderBy[key]\n                return orderBy\n            }, {} as OrderByCondition)\n        }\n\n        return this.orderBys\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates a main alias and adds it to the current expression map.\n     */\n    setMainAlias(alias: Alias): Alias {\n        // if main alias is already set then remove it from the array\n        // if (this.mainAlias)\n        //     this.aliases.splice(this.aliases.indexOf(this.mainAlias));\n\n        // set new main alias\n        this.mainAlias = alias\n\n        return alias\n    }\n\n    /**\n     * Creates a new alias and adds it to the current expression map.\n     */\n    createAlias(options: {\n        type: \"from\" | \"select\" | \"join\" | \"other\"\n        name?: string\n        target?: Function | string\n        tablePath?: string\n        subQuery?: string\n        metadata?: EntityMetadata\n    }): Alias {\n        let aliasName = options.name\n        if (!aliasName && options.tablePath) aliasName = options.tablePath\n        if (!aliasName && typeof options.target === \"function\")\n            aliasName = options.target.name\n        if (!aliasName && typeof options.target === \"string\")\n            aliasName = options.target\n\n        const alias = new Alias()\n        alias.type = options.type\n        if (aliasName) alias.name = aliasName\n        if (options.metadata) alias.metadata = options.metadata\n        if (options.target && !alias.hasMetadata)\n            alias.metadata = this.connection.getMetadata(options.target)\n        if (options.tablePath) alias.tablePath = options.tablePath\n        if (options.subQuery) alias.subQuery = options.subQuery\n\n        this.aliases.push(alias)\n        return alias\n    }\n\n    /**\n     * Finds alias with the given name.\n     * If alias was not found it throw an exception.\n     */\n    findAliasByName(aliasName: string): Alias {\n        const alias = this.aliases.find((alias) => alias.name === aliasName)\n        if (!alias)\n            throw new TypeORMError(\n                `\"${aliasName}\" alias was not found. Maybe you forgot to join it?`,\n            )\n\n        return alias\n    }\n\n    findColumnByAliasExpression(\n        aliasExpression: string,\n    ): ColumnMetadata | undefined {\n        const [aliasName, propertyPath] = aliasExpression.split(\".\")\n        const alias = this.findAliasByName(aliasName)\n        return alias.metadata.findColumnWithPropertyName(propertyPath)\n    }\n\n    /**\n     * Gets relation metadata of the relation this query builder works with.\n     *\n     * todo: add proper exceptions\n     */\n    get relationMetadata(): RelationMetadata {\n        if (!this.mainAlias)\n            throw new TypeORMError(`Entity to work with is not specified!`) // todo: better message\n\n        const relationMetadata =\n            this.mainAlias.metadata.findRelationWithPropertyPath(\n                this.relationPropertyPath,\n            )\n        if (!relationMetadata)\n            throw new TypeORMError(\n                `Relation ${this.relationPropertyPath} was not found in entity ${this.mainAlias.name}`,\n            ) // todo: better message\n\n        return relationMetadata\n    }\n\n    /**\n     * Copies all properties of the current QueryExpressionMap into a new one.\n     * Useful when QueryBuilder needs to create a copy of itself.\n     */\n    clone(): QueryExpressionMap {\n        const map = new QueryExpressionMap(this.connection)\n        map.queryType = this.queryType\n        map.selects = this.selects.map((select) => select)\n        map.maxExecutionTime = this.maxExecutionTime\n        map.selectDistinct = this.selectDistinct\n        map.selectDistinctOn = this.selectDistinctOn\n        this.aliases.forEach((alias) => map.aliases.push(new Alias(alias)))\n        map.relationLoadStrategy = this.relationLoadStrategy\n        map.mainAlias = this.mainAlias\n        map.valuesSet = this.valuesSet\n        map.returning = this.returning\n        map.onConflict = this.onConflict\n        map.onIgnore = this.onIgnore\n        map.onUpdate = this.onUpdate\n        map.joinAttributes = this.joinAttributes.map(\n            (join) => new JoinAttribute(this.connection, this, join),\n        )\n        map.relationIdAttributes = this.relationIdAttributes.map(\n            (relationId) => new RelationIdAttribute(this, relationId),\n        )\n        map.relationCountAttributes = this.relationCountAttributes.map(\n            (relationCount) => new RelationCountAttribute(this, relationCount),\n        )\n        map.wheres = this.wheres.map((where) => ({ ...where }))\n        map.havings = this.havings.map((having) => ({ ...having }))\n        map.orderBys = Object.assign({}, this.orderBys)\n        map.groupBys = this.groupBys.map((groupBy) => groupBy)\n        map.limit = this.limit\n        map.offset = this.offset\n        map.skip = this.skip\n        map.take = this.take\n        map.lockMode = this.lockMode\n        map.onLocked = this.onLocked\n        map.lockVersion = this.lockVersion\n        map.lockTables = this.lockTables\n        map.withDeleted = this.withDeleted\n        map.parameters = Object.assign({}, this.parameters)\n        map.disableEscaping = this.disableEscaping\n        map.enableRelationIdValues = this.enableRelationIdValues\n        map.extraAppendedAndWhereCondition = this.extraAppendedAndWhereCondition\n        map.subQuery = this.subQuery\n        map.aliasNamePrefixingEnabled = this.aliasNamePrefixingEnabled\n        map.cache = this.cache\n        map.cacheId = this.cacheId\n        map.cacheDuration = this.cacheDuration\n        map.relationPropertyPath = this.relationPropertyPath\n        map.of = this.of\n        map.insertColumns = this.insertColumns\n        map.whereEntities = this.whereEntities\n        map.updateEntity = this.updateEntity\n        map.callListeners = this.callListeners\n        map.useTransaction = this.useTransaction\n        map.timeTravel = this.timeTravel\n        map.nativeParameters = Object.assign({}, this.nativeParameters)\n        map.comment = this.comment\n        map.commonTableExpressions = this.commonTableExpressions.map(\n            (cteOptions) => ({\n                alias: cteOptions.alias,\n                queryBuilder:\n                    typeof cteOptions.queryBuilder === \"string\"\n                        ? cteOptions.queryBuilder\n                        : cteOptions.queryBuilder.clone(),\n                options: cteOptions.options,\n            }),\n        )\n        return map\n    }\n}\n"], "sourceRoot": ".."}