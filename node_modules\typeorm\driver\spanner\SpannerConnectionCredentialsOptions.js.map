{"version": 3, "sources": ["../../src/driver/spanner/SpannerConnectionCredentialsOptions.ts"], "names": [], "mappings": "", "file": "SpannerConnectionCredentialsOptions.js", "sourcesContent": ["/**\n * Spanner specific connection credential options.\n */\nexport interface SpannerConnectionCredentialsOptions {\n    /**\n     * Connection url where the connection is performed.\n     */\n    readonly instanceId?: string\n\n    /**\n     * Database host.\n     */\n    readonly projectId?: string\n\n    /**\n     * Database host port.\n     */\n    readonly databaseId?: string\n\n    /**\n     * Object containing client_email and private_key properties, or the external account client options. Cannot be used with apiKey.\n     */\n    readonly credentials?: {\n        /**\n         * Client email connection credentials (Optional)\n         */\n        readonly client_email: string\n\n        /**\n         * Private key connection credentials (Optional)\n         */\n        readonly private_key: string\n    }\n}\n"], "sourceRoot": "../.."}