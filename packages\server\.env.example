# Server Configuration
NODE_ENV=development
PORT=3000
HOST=localhost

# Database Configuration
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=rage_rp
DB_PASSWORD=your_password_here
DB_DATABASE=rage_rp_db
DB_SYNCHRONIZE=false
DB_LOGGING=true

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your_refresh_secret_here
JWT_REFRESH_EXPIRES_IN=30d

# Encryption
BCRYPT_ROUNDS=12

# Logging
LOG_LEVEL=debug
LOG_FILE=true
LOG_CONSOLE=true

# RAGE MP Configuration
RAGEMP_HOST=localhost
RAGEMP_PORT=22005
RAGEMP_MAX_PLAYERS=200

# Redis Configuration (for caching and sessions)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Security
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# Development
DEBUG=true
HOT_RELOAD=true
