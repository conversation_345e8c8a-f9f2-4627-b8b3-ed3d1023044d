<!doctype html>
<html lang="fa" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>RAGE RP | نسل جدید رول‌پلی</title>
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Meta tags for SEO and social sharing -->
    <meta name="description" content="سرور رول‌پلی نسل جدید RAGE Multiplayer با تکنولوژی‌های مدرن و تجربه بی‌نظیر">
    <meta name="keywords" content="RAGE MP, Roleplay, GTA V, Gaming, Persian, فارسی">
    <meta name="author" content="RAGE RP Team">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://rage-rp.com/">
    <meta property="og:title" content="RAGE RP | نسل جدید رول‌پلی">
    <meta property="og:description" content="سرور رول‌پلی نسل جدید RAGE Multiplayer با تکنولوژی‌های مدرن و تجربه بی‌نظیر">
    <meta property="og:image" content="/og-image.jpg">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://rage-rp.com/">
    <meta property="twitter:title" content="RAGE RP | نسل جدید رول‌پلی">
    <meta property="twitter:description" content="سرور رول‌پلی نسل جدید RAGE Multiplayer با تکنولوژی‌های مدرن و تجربه بی‌نظیر">
    <meta property="twitter:image" content="/og-image.jpg">
    
    <!-- Disable zoom on mobile -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    
    <!-- Prevent text size adjust on iOS -->
    <meta name="format-detection" content="telephone=no">
    
    <!-- Theme color -->
    <meta name="theme-color" content="#0ea5e9">
    
    <!-- Disable context menu and text selection for game UI -->
    <style>
      * {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-touch-callout: none;
        -webkit-tap-highlight-color: transparent;
      }
      
      body {
        margin: 0;
        padding: 0;
        overflow: hidden;
        font-family: 'Inter', system-ui, sans-serif;
        background: transparent;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      
      #root {
        width: 100vw;
        height: 100vh;
        position: fixed;
        top: 0;
        left: 0;
        pointer-events: none;
      }
      
      .ui-interactive {
        pointer-events: auto;
      }
      
      /* Custom scrollbar */
      ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      
      ::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 3px;
      }
      
      ::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.3);
        border-radius: 3px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.5);
      }
      
      /* Loading animation */
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(14, 165, 233, 0.1);
        border-left: 4px solid #0ea5e9;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="root">
      <!-- Loading fallback -->
      <div id="loading" style="
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        color: #0ea5e9;
        font-weight: 500;
      ">
        <div class="loading-spinner"></div>
        <div>در حال بارگذاری...</div>
      </div>
    </div>
    
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- Disable right-click context menu -->
    <script>
      document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
      });
      
      // Disable F12, Ctrl+Shift+I, Ctrl+U
      document.addEventListener('keydown', function(e) {
        if (e.key === 'F12' || 
            (e.ctrlKey && e.shiftKey && e.key === 'I') ||
            (e.ctrlKey && e.key === 'u')) {
          e.preventDefault();
        }
      });
      
      // Hide loading when React app loads
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.display = 'none';
          }
        }, 1000);
      });
    </script>
  </body>
</html>
