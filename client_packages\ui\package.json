{"name": "@rage-rp/ui", "version": "1.0.0", "description": "RAGE MP Roleplay Server - Frontend UI", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,css,scss}\"", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "keywords": ["rage-mp", "roleplay", "react", "typescript", "ui", "gaming"], "author": "RAGE RP Team", "license": "MIT", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3", "zustand": "^4.5.2", "framer-motion": "^11.0.24", "lucide-react": "^0.363.0", "clsx": "^2.1.0", "tailwind-merge": "^2.2.2", "react-hook-form": "^7.51.1", "zod": "^3.22.4", "@hookform/resolvers": "^3.3.4", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "axios": "^1.6.8"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "prettier": "^3.2.5", "typescript": "^5.2.2", "vite": "^5.2.0", "vitest": "^1.4.0", "@vitest/ui": "^1.4.0", "@vitest/coverage-v8": "^1.4.0", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "tailwindcss": "^3.4.1", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10"}, "engines": {"node": ">=18.0.0"}}