import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';

// Import pages and components
import LoginPage from '@/pages/LoginPage';
import CharacterSelection from '@/pages/CharacterSelection';
import HUD from '@/components/HUD/HUD';
import Chat from '@/components/Chat/Chat';
import Inventory from '@/components/Inventory/Inventory';
import VehicleHUD from '@/components/Vehicle/VehicleHUD';
import AdminPanel from '@/pages/AdminPanel';

// Import hooks and stores
import { useGameStore } from '@/hooks/useGameStore';
import { useUIStore } from '@/hooks/useUIStore';

/**
 * Main App component
 * Handles routing and global UI state management
 */
function App(): JSX.Element {
  const { isLoggedIn, isInGame, currentCharacter } = useGameStore();
  const { 
    showLogin, 
    showCharacterSelection, 
    showInventory, 
    showAdminPanel,
    showChat,
    showHUD,
    showVehicleHUD 
  } = useUIStore();

  return (
    <div className="app-container w-full h-full relative">
      {/* Background overlay for UI elements */}
      <div className="ui-overlay absolute inset-0 pointer-events-none">
        
        {/* Authentication Flow */}
        <AnimatePresence>
          {showLogin && !isLoggedIn && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 pointer-events-auto"
            >
              <LoginPage />
            </motion.div>
          )}
        </AnimatePresence>

        <AnimatePresence>
          {showCharacterSelection && isLoggedIn && !isInGame && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 pointer-events-auto"
            >
              <CharacterSelection />
            </motion.div>
          )}
        </AnimatePresence>

        {/* In-Game UI Elements */}
        {isInGame && currentCharacter && (
          <>
            {/* Main HUD */}
            <AnimatePresence>
              {showHUD && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 20 }}
                  className="fixed bottom-4 left-4 pointer-events-auto"
                >
                  <HUD />
                </motion.div>
              )}
            </AnimatePresence>

            {/* Chat System */}
            <AnimatePresence>
              {showChat && (
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="fixed bottom-4 left-4 pointer-events-auto"
                >
                  <Chat />
                </motion.div>
              )}
            </AnimatePresence>

            {/* Vehicle HUD */}
            <AnimatePresence>
              {showVehicleHUD && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  className="fixed bottom-4 right-4 pointer-events-auto"
                >
                  <VehicleHUD />
                </motion.div>
              )}
            </AnimatePresence>

            {/* Inventory */}
            <AnimatePresence>
              {showInventory && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  className="fixed inset-0 z-40 pointer-events-auto"
                >
                  <Inventory />
                </motion.div>
              )}
            </AnimatePresence>

            {/* Admin Panel */}
            <AnimatePresence>
              {showAdminPanel && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  className="fixed inset-0 z-50 pointer-events-auto"
                >
                  <AdminPanel />
                </motion.div>
              )}
            </AnimatePresence>
          </>
        )}

        {/* Global Notifications */}
        <div className="fixed top-4 right-4 z-50 pointer-events-none">
          {/* Notifications will be rendered here by react-hot-toast */}
        </div>

        {/* Debug Info (Development Only) */}
        {__DEV__ && (
          <div className="fixed top-4 left-4 glass-dark rounded-lg p-2 text-xs text-white z-50">
            <div>Logged In: {isLoggedIn ? 'Yes' : 'No'}</div>
            <div>In Game: {isInGame ? 'Yes' : 'No'}</div>
            <div>Character: {currentCharacter?.name || 'None'}</div>
          </div>
        )}
      </div>

      {/* Router for page-based navigation (if needed) */}
      <Routes>
        <Route path="/" element={<div />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/character-selection" element={<CharacterSelection />} />
        <Route path="/admin" element={<AdminPanel />} />
      </Routes>
    </div>
  );
}

export default App;
