import { EventEmitter } from 'events';
import { Logger } from '@/utils/Logger';
import { IEventHandler } from '@/types';

/**
 * Centralized event management system
 * Handles both internal server events and RAGE MP events
 */
export class EventManager extends EventEmitter {
  private static instance: EventManager;
  private readonly logger: Logger;
  private readonly handlers = new Map<string, Set<Function>>();
  private isInitialized = false;

  private constructor() {
    super();
    this.logger = Logger.getInstance();
    this.setMaxListeners(100); // Increase max listeners for complex systems
  }

  public static getInstance(): EventManager {
    if (!EventManager.instance) {
      EventManager.instance = new EventManager();
    }
    return EventManager.instance;
  }

  /**
   * Initialize the event manager
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      throw new Error('EventManager is already initialized');
    }

    try {
      // Setup error handling for events
      this.setupErrorHandling();

      this.isInitialized = true;
      this.logger.info('EventManager initialized successfully');

    } catch (error) {
      this.logger.error('Failed to initialize EventManager:', error);
      throw error;
    }
  }

  /**
   * Shutdown the event manager
   */
  public async shutdown(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      // Remove all listeners
      this.removeAllListeners();
      this.handlers.clear();

      this.isInitialized = false;
      this.logger.info('EventManager shutdown successfully');

    } catch (error) {
      this.logger.error('Failed to shutdown EventManager:', error);
      throw error;
    }
  }

  /**
   * Register an event handler
   */
  public on(event: string, handler: (...args: any[]) => void): this {
    super.on(event, handler);

    // Track handlers for statistics
    if (!this.handlers.has(event)) {
      this.handlers.set(event, new Set());
    }
    this.handlers.get(event)!.add(handler);

    this.logger.debug(`Event handler registered for: ${event}`);
    return this;
  }

  /**
   * Register a one-time event handler
   */
  public once(event: string, handler: (...args: any[]) => void): this {
    super.once(event, handler);

    // Track handlers for statistics
    if (!this.handlers.has(event)) {
      this.handlers.set(event, new Set());
    }
    this.handlers.get(event)!.add(handler);

    this.logger.debug(`One-time event handler registered for: ${event}`);
    return this;
  }

  /**
   * Remove an event handler
   */
  public off(event: string, handler: (...args: any[]) => void): this {
    super.off(event, handler);

    // Remove from tracking
    if (this.handlers.has(event)) {
      this.handlers.get(event)!.delete(handler);
      if (this.handlers.get(event)!.size === 0) {
        this.handlers.delete(event);
      }
    }

    this.logger.debug(`Event handler removed for: ${event}`);
    return this;
  }

  /**
   * Emit an event with error handling
   */
  public emit(event: string, ...args: any[]): boolean {
    try {
      this.logger.debug(`Emitting event: ${event}`, { args: args.length });
      return super.emit(event, ...args);
    } catch (error) {
      this.logger.error(`Error emitting event '${event}':`, error);
      return false;
    }
  }

  /**
   * Emit an event asynchronously
   */
  public async emitAsync(event: string, ...args: any[]): Promise<void> {
    try {
      this.logger.debug(`Emitting async event: ${event}`, { args: args.length });
      
      const listeners = this.listeners(event);
      const promises: Promise<any>[] = [];

      for (const listener of listeners) {
        try {
          const result = (listener as Function)(...args);
          if (result instanceof Promise) {
            promises.push(result);
          }
        } catch (error) {
          this.logger.error(`Error in event listener for '${event}':`, error);
        }
      }

      if (promises.length > 0) {
        await Promise.allSettled(promises);
      }

    } catch (error) {
      this.logger.error(`Error emitting async event '${event}':`, error);
    }
  }

  /**
   * Register multiple event handlers at once
   */
  public registerHandlers(handlers: IEventHandler[]): void {
    for (const { event, handler } of handlers) {
      this.on(event, handler);
    }
    this.logger.info(`Registered ${handlers.length} event handlers`);
  }

  /**
   * Remove all handlers for a specific event
   */
  public removeAllHandlers(event: string): void {
    this.removeAllListeners(event);
    this.handlers.delete(event);
    this.logger.debug(`All handlers removed for event: ${event}`);
  }

  /**
   * Get the number of handlers for an event
   */
  public getHandlerCount(event?: string): number {
    if (event) {
      return this.listenerCount(event);
    }
    
    let total = 0;
    for (const handlers of this.handlers.values()) {
      total += handlers.size;
    }
    return total;
  }

  /**
   * Get all registered events
   */
  public getRegisteredEvents(): string[] {
    return Array.from(this.handlers.keys());
  }

  /**
   * Check if an event has any handlers
   */
  public hasHandlers(event: string): boolean {
    return this.listenerCount(event) > 0;
  }

  /**
   * Setup error handling for events
   */
  private setupErrorHandling(): void {
    this.on('error', (error: Error) => {
      this.logger.error('EventManager error:', error);
    });

    // Handle uncaught exceptions in event handlers
    process.on('uncaughtException', (error: Error) => {
      this.logger.error('Uncaught exception in event handler:', error);
    });

    process.on('unhandledRejection', (reason: any) => {
      this.logger.error('Unhandled rejection in event handler:', reason);
    });
  }

  /**
   * Get event statistics
   */
  public getStats(): Record<string, any> {
    const stats: Record<string, any> = {
      totalEvents: this.handlers.size,
      totalHandlers: this.getHandlerCount(),
      events: {},
    };

    for (const [event, handlers] of this.handlers.entries()) {
      stats.events[event] = handlers.size;
    }

    return stats;
  }

  /**
   * Create a namespaced event emitter
   */
  public createNamespace(namespace: string): NamespacedEventManager {
    return new NamespacedEventManager(this, namespace);
  }
}

/**
 * Namespaced event manager for module-specific events
 */
export class NamespacedEventManager {
  constructor(
    private readonly eventManager: EventManager,
    private readonly namespace: string
  ) {}

  public on(event: string, handler: (...args: any[]) => void): this {
    this.eventManager.on(`${this.namespace}:${event}`, handler);
    return this;
  }

  public once(event: string, handler: (...args: any[]) => void): this {
    this.eventManager.once(`${this.namespace}:${event}`, handler);
    return this;
  }

  public off(event: string, handler: (...args: any[]) => void): this {
    this.eventManager.off(`${this.namespace}:${event}`, handler);
    return this;
  }

  public emit(event: string, ...args: any[]): boolean {
    return this.eventManager.emit(`${this.namespace}:${event}`, ...args);
  }

  public async emitAsync(event: string, ...args: any[]): Promise<void> {
    return this.eventManager.emitAsync(`${this.namespace}:${event}`, ...args);
  }
}
