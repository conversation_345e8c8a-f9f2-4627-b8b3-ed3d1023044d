// Global Types for RAGE MP Roleplay Server

export interface IPlayer extends PlayerMp {
  id: number;
  characterId?: number;
  accountId?: number;
  isLoggedIn: boolean;
  isAuthenticated: boolean;
  lastActivity: Date;
  sessionData: Record<string, unknown>;
}

export interface IVehicle extends VehicleMp {
  id: number;
  ownerId?: number;
  faction?: string;
  locked: boolean;
  fuel: number;
  mileage: number;
  customization: VehicleCustomization;
}

export interface VehicleCustomization {
  color: {
    primary: number;
    secondary: number;
  };
  modifications: Record<string, number>;
  tuning: Record<string, number>;
  neonColor?: [number, number, number];
  windowTint?: number;
}

export interface ICharacter {
  id: number;
  accountId: number;
  name: string;
  surname: string;
  age: number;
  gender: 'male' | 'female';
  appearance: CharacterAppearance;
  stats: CharacterStats;
  position: Vector3;
  dimension: number;
  health: number;
  armor: number;
  money: number;
  bankMoney: number;
  job?: string;
  faction?: string;
  rank?: number;
  playTime: number;
  lastLogin: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CharacterAppearance {
  skin: number;
  face: Record<string, number>;
  hair: {
    style: number;
    color: number;
    highlight: number;
  };
  clothes: Record<string, ClothingItem>;
  accessories: Record<string, AccessoryItem>;
}

export interface ClothingItem {
  drawable: number;
  texture: number;
  palette: number;
}

export interface AccessoryItem {
  drawable: number;
  texture: number;
  palette: number;
}

export interface CharacterStats {
  strength: number;
  stamina: number;
  shooting: number;
  flying: number;
  driving: number;
  stealth: number;
  lung_capacity: number;
}

export interface IAccount {
  id: number;
  username: string;
  email: string;
  password: string;
  salt: string;
  adminLevel: number;
  isBanned: boolean;
  banReason?: string;
  banExpires?: Date;
  registrationDate: Date;
  lastLogin: Date;
  totalPlayTime: number;
  characters: ICharacter[];
}

export interface IFaction {
  id: number;
  name: string;
  type: FactionType;
  color: string;
  members: FactionMember[];
  vehicles: number[];
  properties: number[];
  bank: number;
  settings: Record<string, unknown>;
}

export enum FactionType {
  GOVERNMENT = 'government',
  POLICE = 'police',
  MEDICAL = 'medical',
  GANG = 'gang',
  MAFIA = 'mafia',
  BUSINESS = 'business',
  NEWS = 'news',
}

export interface FactionMember {
  characterId: number;
  rank: number;
  joinDate: Date;
  permissions: string[];
}

export interface IProperty {
  id: number;
  type: PropertyType;
  name: string;
  description: string;
  position: Vector3;
  interior: Vector3;
  price: number;
  ownerId?: number;
  ownerType: 'character' | 'faction';
  locked: boolean;
  settings: PropertySettings;
}

export enum PropertyType {
  HOUSE = 'house',
  APARTMENT = 'apartment',
  BUSINESS = 'business',
  WAREHOUSE = 'warehouse',
  GARAGE = 'garage',
}

export interface PropertySettings {
  maxOccupants: number;
  hasGarage: boolean;
  garageSlots: number;
  hasStorage: boolean;
  storageCapacity: number;
  utilities: {
    electricity: boolean;
    water: boolean;
    internet: boolean;
  };
}

export interface Vector3 {
  x: number;
  y: number;
  z: number;
}

export interface IJob {
  id: string;
  name: string;
  description: string;
  requirements: JobRequirements;
  salary: JobSalary;
  ranks: JobRank[];
  locations: Vector3[];
}

export interface JobRequirements {
  minAge: number;
  minLevel: number;
  requiredLicense?: string[];
  cleanRecord: boolean;
}

export interface JobSalary {
  base: number;
  bonus: number;
  payInterval: number; // minutes
}

export interface JobRank {
  id: number;
  name: string;
  salary: number;
  permissions: string[];
}

// Event System Types
export interface IEventHandler {
  event: string;
  handler: (...args: unknown[]) => void | Promise<void>;
}

export interface IModule {
  name: string;
  version: string;
  dependencies?: string[];
  initialize(): Promise<void>;
  shutdown(): Promise<void>;
}

// Database Types
export interface DatabaseConfig {
  type: 'mysql' | 'postgresql';
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  synchronize: boolean;
  logging: boolean;
}

// API Response Types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: Date;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Utility Types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequireAtLeastOne<T, Keys extends keyof T = keyof T> = Pick<T, Exclude<keyof T, Keys>> &
  {
    [K in Keys]-?: Required<Pick<T, K>> & Partial<Pick<T, Exclude<Keys, K>>>;
  }[Keys];
