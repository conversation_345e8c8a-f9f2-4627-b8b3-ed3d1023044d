{"version": 3, "sources": ["../browser/src/driver/mongodb/MongoDriver.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,uBAAuB,EAAE,MAAM,qCAAqC,CAAA;AAC7E,OAAO,EAAE,8BAA8B,EAAE,MAAM,4CAA4C,CAAA;AAE3F,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAA;AAGrD,OAAO,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAA;AAK5D,OAAO,EAAE,kBAAkB,EAAE,MAAM,yCAAyC,CAAA;AAK5E,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAA;AACpD,OAAO,EAAE,sBAAsB,EAAE,MAAM,mCAAmC,CAAA;AAE1E,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAA;AAC5C,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAA;AAI1C,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAA;AAG5D;;GAEG;AACH,MAAM,OAAO,WAAW;IAwLpB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAsB,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;QA9J5C;;WAEG;QACH,iBAAY,GAAY,KAAK,CAAA;QAE7B;;WAEG;QACH,gBAAW,GAAG,KAAK,CAAA;QAEnB;;WAEG;QACH,uBAAkB,GAAG,MAAe,CAAA;QAEpC;;WAEG;QACH,uBAAkB,GAAiB,EAAE,CAAA;QAOrC;;WAEG;QACH,iBAAY,GAAiB,EAAE,CAAA;QAE/B;;WAEG;QACH,0BAAqB,GAAiB,EAAE,CAAA;QAExC;;WAEG;QACH,6BAAwB,GAAiB,EAAE,CAAA;QAE3C;;WAEG;QACH,yBAAoB,GAAiB,EAAE,CAAA;QAEvC;;WAEG;QACH,oBAAe,GAAsB;YACjC,UAAU,EAAE,KAAK;YACjB,iBAAiB,EAAE,EAAE;YACrB,UAAU,EAAE,KAAK;YACjB,iBAAiB,EAAE,EAAE;YACrB,UAAU,EAAE,KAAK;YACjB,kBAAkB,EAAE,IAAI;YACxB,OAAO,EAAE,KAAK;YACd,SAAS,EAAE,KAAK;YAChB,WAAW,EAAE,KAAK;YAClB,aAAa,EAAE,KAAK;YACpB,kBAAkB,EAAE,KAAK;YACzB,OAAO,EAAE,KAAK;YACd,eAAe,EAAE,KAAK;YACtB,SAAS,EAAE,KAAK;YAChB,aAAa,EAAE,KAAK;YACpB,UAAU,EAAE,KAAK;YACjB,WAAW,EAAE,KAAK;YAClB,YAAY,EAAE,KAAK;YACnB,gBAAgB,EAAE,KAAK;YACvB,cAAc,EAAE,KAAK;YACrB,aAAa,EAAE,KAAK;YACpB,YAAY,EAAE,KAAK;YACnB,aAAa,EAAE,KAAK;SACvB,CAAA;QAcD,oBAAe,GAAoB;YAC/B,OAAO,EAAE,KAAK;SACjB,CAAA;QAED,4EAA4E;QAC5E,uBAAuB;QACvB,4EAA4E;QAE5E;;;WAGG;QACO,qBAAgB,GAAa;YACnC,SAAS;YACT,eAAe;YACf,YAAY;YACZ,gBAAgB;YAChB,qBAAqB;YACrB,aAAa;YACb,kBAAkB;YAClB,kBAAkB;YAClB,QAAQ;YACR,qBAAqB;YACrB,iBAAiB;YACjB,WAAW;YACX,uBAAuB;YACvB,kBAAkB;YAClB,qBAAqB;YACrB,aAAa;YACb,iBAAiB;YACjB,SAAS;YACT,WAAW;YACX,gBAAgB;YAChB,cAAc;YACd,eAAe;YACf,KAAK;YACL,aAAa;YACb,gBAAgB;YAChB,oBAAoB;YACpB,YAAY;YACZ,aAAa;YACb,oBAAoB;YACpB,iBAAiB;YACjB,KAAK;YACL,OAAO;YACP,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,SAAS;YACT,aAAa;YACb,KAAK;YACL,6BAA6B;YAC7B,WAAW;YACX,uBAAuB;YACvB,+BAA+B;YAC/B,GAAG;YACH,cAAc;YACd,YAAY;YACZ,kCAAkC;YAClC,kCAAkC;YAClC,SAAS;YACT,OAAO;YACP,GAAG;YACH,iBAAiB;YACjB,oBAAoB;YACpB,UAAU;SACb,CAAA;QAOG,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAiC,CAAA;QAE3D,sGAAsG;QACtG,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;QAExC,uBAAuB;QACvB,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAEvB,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,yBAAyB,CACjD,IAAI,CAAC,OAAO,CACf,CAAC,QAAQ,CAAA;IACd,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,MAAM,OAAO,GAAG,WAAW,CAAC,yBAAyB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAEnE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CACjD,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAChC,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CACvC,CAAA;QAED,IAAI,CAAC,WAAW,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;QAChE,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE;YACjC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO;SACnC,CAAC,CAAA;IACN,CAAC;IAED,YAAY;QACR,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAA;QACpC,IAAI,CAAC,WAAW,EAAE,CAAC;YACf,MAAM,IAAI,uBAAuB,CAAC,SAAS,CAAC,CAAA;QAChD,CAAC;QAED,IAAI,CAAC,WAAW,GAAG,SAAS,CAAA;QAC5B,MAAM,WAAW,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,mBAAmB;QACf,OAAO,IAAI,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAqB;QACnC,OAAO,IAAI,CAAC,WAAY,CAAA;IAC5B,CAAC;IAED;;;OAGG;IACH,yBAAyB,CACrB,GAAW,EACX,UAAyB,EACzB,gBAA+B;QAE/B,MAAM,IAAI,YAAY,CAClB,oDAAoD,CACvD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAkB;QACrB,OAAO,UAAU,CAAA;IACrB,CAAC;IAED;;;OAGG;IACH,cAAc,CACV,SAAiB,EACjB,MAAe,EACf,QAAiB;QAEjB,OAAO,SAAS,CAAA;IACpB,CAAC;IAED;;OAEG;IACH,cAAc,CACV,MAAgE;QAEhE,IAAI,eAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3C,OAAO;gBACH,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,IAAI,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YACpE,OAAO;gBACH,SAAS,EAAE,MAAM,CAAC,IAAI;aACzB,CAAA;QACL,CAAC;QAED,IAAI,eAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5C,OAAO;gBACH,SAAS,EAAE,MAAM,CAAC,mBAAmB;aACxC,CAAA;QACL,CAAC;QAED,OAAO;YACH,SAAS,EAAE,MAAM;SACpB,CAAA;IACL,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,KAAU,EAAE,cAA8B;QAC7D,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,sBAAsB,CAAC,WAAW,CACtC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QACL,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,KAAU,EAAE,cAA8B;QAC3D,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,sBAAsB,CAAC,aAAa,CACxC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QACL,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAKb;QACG,MAAM,IAAI,YAAY,CAClB,uDAAuD,CAC1D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,cAA8B;QAC3C,MAAM,IAAI,YAAY,CAClB,uDAAuD,CAC1D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,MAAsB;QACpC,MAAM,IAAI,YAAY,CAClB,uDAAuD,CAC1D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,MAAsB;QAClC,MAAM,IAAI,YAAY,CAClB,uDAAuD,CAC1D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAmB;QAC9B,MAAM,IAAI,YAAY,CAClB,uDAAuD,CAC1D,CAAA;IACL,CAAC;IAED;;;;OAIG;IACH,sBAAsB;QAClB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;;;OAIG;IACH,qBAAqB;QACjB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,QAAwB,EAAE,UAAe;QACxD,OAAO,QAAQ,CAAC,cAAe,CAAC,cAAc,CAAC,UAAU,CAAC,CAAA;IAC9D,CAAC;IAED;;;OAGG;IACH,kBAAkB,CACd,YAA2B,EAC3B,eAAiC;QAEjC,MAAM,IAAI,YAAY,CAClB,uDAAuD,CAC1D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,uBAAuB;QACnB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,yBAAyB;QACrB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,6BAA6B;QACzB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,aAAqB,EAAE,KAAa;QAChD,OAAO,EAAE,CAAA;IACb,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,eAAe,CAAC,OAA0B;QAChD,YAAY;QACZ,sBAAsB;QACtB,6BAA6B;QAC7B,yDAAyD;QACzD,IAAI;IACR,CAAC;IAED;;OAEG;IACO,gBAAgB;QACtB,IAAI,CAAC;YACD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACpE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QAC1B,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,MAAM,IAAI,8BAA8B,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;QAClE,CAAC;IACL,CAAC;IAED;;OAEG;IACO,kBAAkB,CAAC,OAA+B;QACxD,MAAM,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAA;QAChD,MAAM,kBAAkB,GACpB,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ;YAChC,CAAC,CAAC,GAAG,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,kBAAkB,CACzD,OAAO,CAAC,QAAQ,CACnB,GAAG;YACN,CAAC,CAAC,EAAE,CAAA;QAEZ,MAAM,WAAW,GACb,aAAa,KAAK,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,EAAE,CAAA;QAExE,IAAI,gBAAwB,CAAA;QAC5B,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACrB,gBAAgB,GAAG,GAAG,aAAa,MAAM,kBAAkB,GACvD,OAAO,CAAC,cAAc;gBACtB,OAAO,CAAC,IAAI,GAAG,WAAW;gBAC1B,WAAW,GAAG,WAClB,IAAI,OAAO,CAAC,QAAQ,IAAI,EAAE,EAAE,CAAA;QAChC,CAAC;aAAM,CAAC;YACJ,gBAAgB,GAAG,GAAG,aAAa,MAAM,kBAAkB,GACvD,OAAO,CAAC,IAAI,IAAI,WACpB,GAAG,WAAW,IAAI,OAAO,CAAC,QAAQ,IAAI,EAAE,EAAE,CAAA;QAC9C,CAAC;QAED,OAAO,gBAAgB,CAAA;IAC3B,CAAC;IAED;;OAEG;IACO,sBAAsB,CAAC,OAA+B;QAC5D,MAAM,YAAY,GAAQ,EAAE,CAAA;QAE5B,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC7C,IAAI,UAAU,IAAI,OAAO,EAAE,CAAC;gBACxB,YAAY,CAAC,UAAU,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAA;YAClD,CAAC;QACL,CAAC;QAED,YAAY,CAAC,UAAU,GAAG;YACtB,IAAI,EAAE,SAAS;SAClB,CAAA;QAED,IAAI,UAAU,IAAI,OAAO,EAAE,CAAC;YACxB,YAAY,CAAC,aAAa,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAA;QACrD,CAAC;QAED,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;QAE1C,OAAO,YAAY,CAAA;IACvB,CAAC;CACJ", "file": "MongoDriver.js", "sourcesContent": ["import { Driver } from \"../Driver\"\nimport { ConnectionIsNotSetError } from \"../../error/ConnectionIsNotSetError\"\nimport { DriverPackageNotInstalledError } from \"../../error/DriverPackageNotInstalledError\"\nimport { CteCapabilities } from \"../types/CteCapabilities\"\nimport { MongoQueryRunner } from \"./MongoQueryRunner\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { ColumnMetadata } from \"../../metadata/ColumnMetadata\"\nimport { PlatformTools } from \"../../platform/PlatformTools\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { MongoConnectionOptions } from \"./MongoConnectionOptions\"\nimport { MappedColumnTypes } from \"../types/MappedColumnTypes\"\nimport { ColumnType } from \"../types/ColumnTypes\"\nimport { MongoSchemaBuilder } from \"../../schema-builder/MongoSchemaBuilder\"\nimport { DataTypeDefaults } from \"../types/DataTypeDefaults\"\nimport { TableColumn } from \"../../schema-builder/table/TableColumn\"\nimport { DataSourceOptions } from \"../../data-source/DataSourceOptions\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { ObjectUtils } from \"../../util/ObjectUtils\"\nimport { ApplyValueTransformers } from \"../../util/ApplyValueTransformers\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { DriverUtils } from \"../DriverUtils\"\nimport { TypeORMError } from \"../../error\"\nimport { Table } from \"../../schema-builder/table/Table\"\nimport { View } from \"../../schema-builder/view/View\"\nimport { TableForeignKey } from \"../../schema-builder/table/TableForeignKey\"\nimport { InstanceChecker } from \"../../util/InstanceChecker\"\nimport { UpsertType } from \"../types/UpsertType\"\n\n/**\n * Organizes communication with MongoDB.\n */\nexport class MongoDriver implements Driver {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Underlying mongodb library.\n     */\n    mongodb: any\n\n    /**\n     * Mongodb does not require to dynamically create query runner each time,\n     * because it does not have a regular connection pool as RDBMS systems have.\n     */\n    queryRunner?: MongoQueryRunner\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection options.\n     */\n    options: MongoConnectionOptions\n\n    /**\n     * Master database used to perform all write queries.\n     */\n    database?: string\n\n    /**\n     * Indicates if replication is enabled.\n     */\n    isReplicated: boolean = false\n\n    /**\n     * Indicates if tree tables are supported by this driver.\n     */\n    treeSupport = false\n\n    /**\n     * Represent transaction support by this driver\n     */\n    transactionSupport = \"none\" as const\n\n    /**\n     * Mongodb does not need to have column types because they are not used in schema sync.\n     */\n    supportedDataTypes: ColumnType[] = []\n\n    /**\n     * Returns type of upsert supported by driver if any\n     */\n    supportedUpsertTypes: UpsertType[]\n\n    /**\n     * Gets list of spatial column data types.\n     */\n    spatialTypes: ColumnType[] = []\n\n    /**\n     * Gets list of column data types that support length by a driver.\n     */\n    withLengthColumnTypes: ColumnType[] = []\n\n    /**\n     * Gets list of column data types that support precision by a driver.\n     */\n    withPrecisionColumnTypes: ColumnType[] = []\n\n    /**\n     * Gets list of column data types that support scale by a driver.\n     */\n    withScaleColumnTypes: ColumnType[] = []\n\n    /**\n     * Mongodb does not need to have a strong defined mapped column types because they are not used in schema sync.\n     */\n    mappedDataTypes: MappedColumnTypes = {\n        createDate: \"int\",\n        createDateDefault: \"\",\n        updateDate: \"int\",\n        updateDateDefault: \"\",\n        deleteDate: \"int\",\n        deleteDateNullable: true,\n        version: \"int\",\n        treeLevel: \"int\",\n        migrationId: \"int\",\n        migrationName: \"int\",\n        migrationTimestamp: \"int\",\n        cacheId: \"int\",\n        cacheIdentifier: \"int\",\n        cacheTime: \"int\",\n        cacheDuration: \"int\",\n        cacheQuery: \"int\",\n        cacheResult: \"int\",\n        metadataType: \"int\",\n        metadataDatabase: \"int\",\n        metadataSchema: \"int\",\n        metadataTable: \"int\",\n        metadataName: \"int\",\n        metadataValue: \"int\",\n    }\n\n    /**\n     * Default values of length, precision and scale depends on column data type.\n     * Used in the cases when length/precision/scale is not specified by user.\n     */\n    dataTypeDefaults: DataTypeDefaults\n\n    /**\n     * No documentation specifying a maximum length for identifiers could be found\n     * for MongoDB.\n     */\n    maxAliasLength?: number\n\n    cteCapabilities: CteCapabilities = {\n        enabled: false,\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Valid mongo connection options\n     * NOTE: Keep in sync with MongoConnectionOptions\n     */\n    protected validOptionNames: string[] = [\n        \"appName\",\n        \"authMechanism\",\n        \"authSource\",\n        \"autoEncryption\",\n        \"checkServerIdentity\",\n        \"compressors\",\n        \"connectTimeoutMS\",\n        \"directConnection\",\n        \"family\",\n        \"forceServerObjectId\",\n        \"ignoreUndefined\",\n        \"keepAlive\",\n        \"keepAliveInitialDelay\",\n        \"localThresholdMS\",\n        \"maxStalenessSeconds\",\n        \"minPoolSize\",\n        \"monitorCommands\",\n        \"noDelay\",\n        \"pkFactory\",\n        \"promoteBuffers\",\n        \"promoteLongs\",\n        \"promoteValues\",\n        \"raw\",\n        \"readConcern\",\n        \"readPreference\",\n        \"readPreferenceTags\",\n        \"replicaSet\",\n        \"retryWrites\",\n        \"serializeFunctions\",\n        \"socketTimeoutMS\",\n        \"ssl\",\n        \"sslCA\",\n        \"sslCRL\",\n        \"sslCert\",\n        \"sslKey\",\n        \"sslPass\",\n        \"sslValidate\",\n        \"tls\",\n        \"tlsAllowInvalidCertificates\",\n        \"tlsCAFile\",\n        \"tlsCertificateKeyFile\",\n        \"tlsCertificateKeyFilePassword\",\n        \"w\",\n        \"writeConcern\",\n        \"wtimeoutMS\",\n        // Undocumented deprecated options\n        // todo: remove next major version\n        \"appname\",\n        \"fsync\",\n        \"j\",\n        \"useNewUrlParser\",\n        \"useUnifiedTopology\",\n        \"wtimeout\",\n    ]\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(protected connection: DataSource) {\n        this.options = connection.options as MongoConnectionOptions\n\n        // validate options to make sure everything is correct and driver will be able to establish connection\n        this.validateOptions(connection.options)\n\n        // load mongodb package\n        this.loadDependencies()\n\n        this.database = DriverUtils.buildMongoDBDriverOptions(\n            this.options,\n        ).database\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Performs connection to the database.\n     */\n    async connect(): Promise<void> {\n        const options = DriverUtils.buildMongoDBDriverOptions(this.options)\n\n        const client = await this.mongodb.MongoClient.connect(\n            this.buildConnectionUrl(options),\n            this.buildConnectionOptions(options),\n        )\n\n        this.queryRunner = new MongoQueryRunner(this.connection, client)\n        ObjectUtils.assign(this.queryRunner, {\n            manager: this.connection.manager,\n        })\n    }\n\n    afterConnect(): Promise<void> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Closes connection with the database.\n     */\n    async disconnect(): Promise<void> {\n        const queryRunner = this.queryRunner\n        if (!queryRunner) {\n            throw new ConnectionIsNotSetError(\"mongodb\")\n        }\n\n        this.queryRunner = undefined\n        await queryRunner.databaseConnection.close()\n    }\n\n    /**\n     * Creates a schema builder used to build and sync a schema.\n     */\n    createSchemaBuilder() {\n        return new MongoSchemaBuilder(this.connection)\n    }\n\n    /**\n     * Creates a query runner used to execute database queries.\n     */\n    createQueryRunner(mode: ReplicationMode) {\n        return this.queryRunner!\n    }\n\n    /**\n     * Replaces parameters in the given sql with special escaping character\n     * and an array of parameter names to be passed to a query.\n     */\n    escapeQueryWithParameters(\n        sql: string,\n        parameters: ObjectLiteral,\n        nativeParameters: ObjectLiteral,\n    ): [string, any[]] {\n        throw new TypeORMError(\n            `This operation is not supported by Mongodb driver.`,\n        )\n    }\n\n    /**\n     * Escapes a column name.\n     */\n    escape(columnName: string): string {\n        return columnName\n    }\n\n    /**\n     * Build full table name with database name, schema name and table name.\n     * E.g. myDB.mySchema.myTable\n     */\n    buildTableName(\n        tableName: string,\n        schema?: string,\n        database?: string,\n    ): string {\n        return tableName\n    }\n\n    /**\n     * Parse a target table name or other types and return a normalized table definition.\n     */\n    parseTableName(\n        target: EntityMetadata | Table | View | TableForeignKey | string,\n    ): { tableName: string; schema?: string; database?: string } {\n        if (InstanceChecker.isEntityMetadata(target)) {\n            return {\n                tableName: target.tableName,\n            }\n        }\n\n        if (InstanceChecker.isTable(target) || InstanceChecker.isView(target)) {\n            return {\n                tableName: target.name,\n            }\n        }\n\n        if (InstanceChecker.isTableForeignKey(target)) {\n            return {\n                tableName: target.referencedTableName,\n            }\n        }\n\n        return {\n            tableName: target,\n        }\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type and metadata.\n     */\n    preparePersistentValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformTo(\n                columnMetadata.transformer,\n                value,\n            )\n        return value\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type or metadata.\n     */\n    prepareHydratedValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformFrom(\n                columnMetadata.transformer,\n                value,\n            )\n        return value\n    }\n\n    /**\n     * Creates a database type from a given column metadata.\n     */\n    normalizeType(column: {\n        type?: ColumnType\n        length?: number | string\n        precision?: number | null\n        scale?: number\n    }): string {\n        throw new TypeORMError(\n            `MongoDB is schema-less, not supported by this driver.`,\n        )\n    }\n\n    /**\n     * Normalizes \"default\" value of the column.\n     */\n    normalizeDefault(columnMetadata: ColumnMetadata): string | undefined {\n        throw new TypeORMError(\n            `MongoDB is schema-less, not supported by this driver.`,\n        )\n    }\n\n    /**\n     * Normalizes \"isUnique\" value of the column.\n     */\n    normalizeIsUnique(column: ColumnMetadata): boolean {\n        throw new TypeORMError(\n            `MongoDB is schema-less, not supported by this driver.`,\n        )\n    }\n\n    /**\n     * Calculates column length taking into account the default length values.\n     */\n    getColumnLength(column: ColumnMetadata): string {\n        throw new TypeORMError(\n            `MongoDB is schema-less, not supported by this driver.`,\n        )\n    }\n\n    /**\n     * Normalizes \"default\" value of the column.\n     */\n    createFullType(column: TableColumn): string {\n        throw new TypeORMError(\n            `MongoDB is schema-less, not supported by this driver.`,\n        )\n    }\n\n    /**\n     * Obtains a new database connection to a master server.\n     * Used for replication.\n     * If replication is not setup then returns default connection's database connection.\n     */\n    obtainMasterConnection(): Promise<any> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Obtains a new database connection to a slave server.\n     * Used for replication.\n     * If replication is not setup then returns master (default) connection's database connection.\n     */\n    obtainSlaveConnection(): Promise<any> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Creates generated map of values generated or returned by database after INSERT query.\n     */\n    createGeneratedMap(metadata: EntityMetadata, insertedId: any) {\n        return metadata.objectIdColumn!.createValueMap(insertedId)\n    }\n\n    /**\n     * Differentiate columns of this table and columns from the given column metadatas columns\n     * and returns only changed.\n     */\n    findChangedColumns(\n        tableColumns: TableColumn[],\n        columnMetadatas: ColumnMetadata[],\n    ): ColumnMetadata[] {\n        throw new TypeORMError(\n            `MongoDB is schema-less, not supported by this driver.`,\n        )\n    }\n\n    /**\n     * Returns true if driver supports RETURNING / OUTPUT statement.\n     */\n    isReturningSqlSupported(): boolean {\n        return false\n    }\n\n    /**\n     * Returns true if driver supports uuid values generation on its own.\n     */\n    isUUIDGenerationSupported(): boolean {\n        return false\n    }\n\n    /**\n     * Returns true if driver supports fulltext indices.\n     */\n    isFullTextColumnTypeSupported(): boolean {\n        return false\n    }\n\n    /**\n     * Creates an escaped parameter.\n     */\n    createParameter(parameterName: string, index: number): string {\n        return \"\"\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Validate driver options to make sure everything is correct and driver will be able to establish connection.\n     */\n    protected validateOptions(options: DataSourceOptions) {\n        // todo: fix\n        // if (!options.url) {\n        //     if (!options.database)\n        //         throw new DriverOptionNotSetError(\"database\");\n        // }\n    }\n\n    /**\n     * Loads all driver dependencies.\n     */\n    protected loadDependencies(): any {\n        try {\n            const mongodb = this.options.driver || PlatformTools.load(\"mongodb\")\n            this.mongodb = mongodb\n        } catch (e) {\n            throw new DriverPackageNotInstalledError(\"MongoDB\", \"mongodb\")\n        }\n    }\n\n    /**\n     * Builds connection url that is passed to underlying driver to perform connection to the mongodb database.\n     */\n    protected buildConnectionUrl(options: { [key: string]: any }): string {\n        const schemaUrlPart = options.type.toLowerCase()\n        const credentialsUrlPart =\n            options.username && options.password\n                ? `${encodeURIComponent(options.username)}:${encodeURIComponent(\n                      options.password,\n                  )}@`\n                : \"\"\n\n        const portUrlPart =\n            schemaUrlPart === \"mongodb+srv\" ? \"\" : `:${options.port || \"27017\"}`\n\n        let connectionString: string\n        if (options.replicaSet) {\n            connectionString = `${schemaUrlPart}://${credentialsUrlPart}${\n                options.hostReplicaSet ||\n                options.host + portUrlPart ||\n                \"127.0.0.1\" + portUrlPart\n            }/${options.database || \"\"}`\n        } else {\n            connectionString = `${schemaUrlPart}://${credentialsUrlPart}${\n                options.host || \"127.0.0.1\"\n            }${portUrlPart}/${options.database || \"\"}`\n        }\n\n        return connectionString\n    }\n\n    /**\n     * Build connection options from MongoConnectionOptions\n     */\n    protected buildConnectionOptions(options: { [key: string]: any }): any {\n        const mongoOptions: any = {}\n\n        for (const optionName of this.validOptionNames) {\n            if (optionName in options) {\n                mongoOptions[optionName] = options[optionName]\n            }\n        }\n\n        mongoOptions.driverInfo = {\n            name: \"TypeORM\",\n        }\n\n        if (\"poolSize\" in options) {\n            mongoOptions[\"maxPoolSize\"] = options[\"poolSize\"]\n        }\n\n        Object.assign(mongoOptions, options.extra)\n\n        return mongoOptions\n    }\n}\n"], "sourceRoot": "../.."}