import { Logger } from '@/utils/Logger';
import { EventManager } from '@/events/EventManager';
import { IVehicle, IPlayer } from '@/types';

/**
 * Manages all vehicle-related operations and state
 */
export class VehicleManager {
  private static instance: VehicleManager;
  private readonly logger: Logger;
  private readonly eventManager: EventManager;
  private readonly vehicles = new Map<number, IVehicle>();
  private isInitialized = false;

  private constructor() {
    this.logger = Logger.getInstance();
    this.eventManager = EventManager.getInstance();
  }

  public static getInstance(): VehicleManager {
    if (!VehicleManager.instance) {
      VehicleManager.instance = new VehicleManager();
    }
    return VehicleManager.instance;
  }

  /**
   * Initialize the vehicle manager
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      throw new Error('VehicleManager is already initialized');
    }

    try {
      // Load vehicles from database
      await this.loadVehicles();

      // Setup periodic tasks
      this.setupPeriodicTasks();

      this.isInitialized = true;
      this.logger.info('VehicleManager initialized successfully');

    } catch (error) {
      this.logger.error('Failed to initialize VehicleManager:', error);
      throw error;
    }
  }

  /**
   * Shutdown the vehicle manager
   */
  public async shutdown(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      // Save all vehicle data
      await this.saveAllVehicles();

      // Destroy all vehicles
      this.destroyAllVehicles();

      this.isInitialized = false;
      this.logger.info('VehicleManager shutdown successfully');

    } catch (error) {
      this.logger.error('Failed to shutdown VehicleManager:', error);
      throw error;
    }
  }

  /**
   * Handle vehicle destroy event
   */
  public onVehicleDestroy(vehicle: VehicleMp): void {
    try {
      const extendedVehicle = this.vehicles.get(vehicle.id);
      if (!extendedVehicle) {
        return;
      }

      // Save vehicle data before destruction
      this.saveVehicleData(extendedVehicle);

      // Emit destroy event
      this.eventManager.emit('vehicle:destroy', extendedVehicle);

      // Remove from tracking
      this.vehicles.delete(vehicle.id);

      this.logger.debug(`Vehicle ${vehicle.id} destroyed`);

    } catch (error) {
      this.logger.error(`Error handling vehicle destroy for ${vehicle.id}:`, error);
    }
  }

  /**
   * Handle player enter vehicle event
   */
  public onPlayerEnterVehicle(player: PlayerMp, vehicle: VehicleMp, seat: number): void {
    try {
      const extendedVehicle = this.vehicles.get(vehicle.id);
      if (!extendedVehicle) {
        return;
      }

      // Emit enter event
      this.eventManager.emit('vehicle:playerEnter', player, extendedVehicle, seat);

      this.logger.debug(`Player ${player.name} entered vehicle ${vehicle.id} (seat: ${seat})`);

    } catch (error) {
      this.logger.error(`Error handling player enter vehicle:`, error);
    }
  }

  /**
   * Handle player exit vehicle event
   */
  public onPlayerExitVehicle(player: PlayerMp, vehicle: VehicleMp): void {
    try {
      const extendedVehicle = this.vehicles.get(vehicle.id);
      if (!extendedVehicle) {
        return;
      }

      // Emit exit event
      this.eventManager.emit('vehicle:playerExit', player, extendedVehicle);

      this.logger.debug(`Player ${player.name} exited vehicle ${vehicle.id}`);

    } catch (error) {
      this.logger.error(`Error handling player exit vehicle:`, error);
    }
  }

  /**
   * Create a new vehicle
   */
  public createVehicle(
    model: string | number,
    position: Vector3Mp,
    heading: number,
    numberPlate?: string,
    alpha?: number,
    locked?: boolean,
    engine?: boolean,
    dimension?: number
  ): IVehicle | null {
    try {
      const vehicle = mp.vehicles.new(model, position, {
        heading,
        numberPlate,
        alpha,
        locked,
        engine,
        dimension,
      });

      if (!vehicle) {
        this.logger.error('Failed to create vehicle');
        return null;
      }

      // Initialize extended vehicle data
      const extendedVehicle = this.initializeVehicle(vehicle);
      this.vehicles.set(vehicle.id, extendedVehicle);

      // Emit create event
      this.eventManager.emit('vehicle:create', extendedVehicle);

      this.logger.debug(`Vehicle created: ${model} (ID: ${vehicle.id})`);
      return extendedVehicle;

    } catch (error) {
      this.logger.error('Error creating vehicle:', error);
      return null;
    }
  }

  /**
   * Get a vehicle by ID
   */
  public getVehicle(id: number): IVehicle | undefined {
    return this.vehicles.get(id);
  }

  /**
   * Get all vehicles
   */
  public getAllVehicles(): IVehicle[] {
    return Array.from(this.vehicles.values());
  }

  /**
   * Get vehicles by owner
   */
  public getVehiclesByOwner(ownerId: number): IVehicle[] {
    return this.getAllVehicles().filter(vehicle => vehicle.ownerId === ownerId);
  }

  /**
   * Get vehicle count
   */
  public getVehicleCount(): number {
    return this.vehicles.size;
  }

  /**
   * Initialize vehicle data
   */
  private initializeVehicle(vehicle: VehicleMp): IVehicle {
    const extendedVehicle = vehicle as IVehicle;
    
    extendedVehicle.id = vehicle.id;
    extendedVehicle.locked = false;
    extendedVehicle.fuel = 100;
    extendedVehicle.mileage = 0;
    extendedVehicle.customization = {
      color: {
        primary: 0,
        secondary: 0,
      },
      modifications: {},
      tuning: {},
    };

    return extendedVehicle;
  }

  /**
   * Load vehicles from database
   */
  private async loadVehicles(): Promise<void> {
    try {
      // TODO: Implement actual database loading
      this.logger.debug('Loading vehicles from database...');
      
      // For now, just log that we would load vehicles
      this.logger.info('Vehicle loading completed (placeholder)');

    } catch (error) {
      this.logger.error('Failed to load vehicles:', error);
      throw error;
    }
  }

  /**
   * Save vehicle data
   */
  private async saveVehicleData(vehicle: IVehicle): Promise<void> {
    try {
      // TODO: Implement actual database save
      this.logger.debug(`Saving data for vehicle ${vehicle.id}`);
    } catch (error) {
      this.logger.error(`Failed to save data for vehicle ${vehicle.id}:`, error);
    }
  }

  /**
   * Save all vehicle data
   */
  private async saveAllVehicles(): Promise<void> {
    const vehicles = this.getAllVehicles();
    
    for (const vehicle of vehicles) {
      await this.saveVehicleData(vehicle);
    }

    this.logger.info(`Saved data for ${vehicles.length} vehicles`);
  }

  /**
   * Destroy all vehicles
   */
  private destroyAllVehicles(): void {
    for (const vehicle of this.getAllVehicles()) {
      try {
        vehicle.destroy();
      } catch (error) {
        this.logger.error(`Error destroying vehicle ${vehicle.id}:`, error);
      }
    }
    
    this.vehicles.clear();
    this.logger.info('All vehicles destroyed');
  }

  /**
   * Setup periodic tasks
   */
  private setupPeriodicTasks(): void {
    // Auto-save vehicle data every 10 minutes
    setInterval(async () => {
      await this.saveAllVehicles();
    }, 10 * 60 * 1000);

    // Update vehicle states every 30 seconds
    setInterval(() => {
      this.updateVehicleStates();
    }, 30 * 1000);
  }

  /**
   * Update vehicle states
   */
  private updateVehicleStates(): void {
    for (const vehicle of this.getAllVehicles()) {
      try {
        // Update fuel consumption for running vehicles
        if (vehicle.engine && vehicle.getOccupants().length > 0) {
          vehicle.fuel = Math.max(0, vehicle.fuel - 0.1);
          
          // Stop engine if out of fuel
          if (vehicle.fuel <= 0) {
            vehicle.engine = false;
            this.eventManager.emit('vehicle:outOfFuel', vehicle);
          }
        }

        // Update mileage
        if (vehicle.engine) {
          // TODO: Calculate actual distance traveled
          vehicle.mileage += 0.01;
        }

      } catch (error) {
        this.logger.error(`Error updating vehicle ${vehicle.id} state:`, error);
      }
    }
  }

  /**
   * Get vehicle statistics
   */
  public getStats(): Record<string, any> {
    const vehicles = this.getAllVehicles();
    const runningVehicles = vehicles.filter(v => v.engine);
    const occupiedVehicles = vehicles.filter(v => v.getOccupants().length > 0);

    return {
      totalVehicles: this.getVehicleCount(),
      runningVehicles: runningVehicles.length,
      occupiedVehicles: occupiedVehicles.length,
      averageFuel: vehicles.length > 0 ? vehicles.reduce((sum, v) => sum + v.fuel, 0) / vehicles.length : 0,
      isInitialized: this.isInitialized,
    };
  }
}
