import { Logger } from '@/utils/Logger';
import { IModule } from '@/types';

/**
 * Manages loading, initialization, and lifecycle of server modules
 * Supports dependency resolution and proper shutdown order
 */
export class ModuleManager {
  private static instance: ModuleManager;
  private readonly logger: Logger;
  private readonly modules = new Map<string, IModule>();
  private readonly loadOrder: string[] = [];
  private isInitialized = false;

  private constructor() {
    this.logger = Logger.getInstance();
  }

  public static getInstance(): ModuleManager {
    if (!ModuleManager.instance) {
      ModuleManager.instance = new ModuleManager();
    }
    return ModuleManager.instance;
  }

  /**
   * Register a module for loading
   */
  public registerModule(module: IModule): void {
    if (this.modules.has(module.name)) {
      throw new Error(`Module '${module.name}' is already registered`);
    }

    this.modules.set(module.name, module);
    this.logger.debug(`Module '${module.name}' registered`);
  }

  /**
   * Load and initialize all registered modules
   */
  public async loadModules(): Promise<void> {
    if (this.isInitialized) {
      throw new Error('Modules are already loaded');
    }

    try {
      // Resolve dependencies and determine load order
      this.resolveLoadOrder();

      // Initialize modules in dependency order
      for (const moduleName of this.loadOrder) {
        const module = this.modules.get(moduleName);
        if (!module) {
          throw new Error(`Module '${moduleName}' not found`);
        }

        this.logger.info(`Loading module: ${module.name} v${module.version}`);
        await module.initialize();
        this.logger.info(`✅ Module '${module.name}' loaded successfully`);
      }

      this.isInitialized = true;
      this.logger.info(`🔧 All modules loaded (${this.modules.size} total)`);

    } catch (error) {
      this.logger.error('Failed to load modules:', error);
      throw error;
    }
  }

  /**
   * Shutdown all modules in reverse order
   */
  public async shutdown(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    const shutdownOrder = [...this.loadOrder].reverse();

    for (const moduleName of shutdownOrder) {
      const module = this.modules.get(moduleName);
      if (!module) {
        continue;
      }

      try {
        this.logger.info(`Shutting down module: ${module.name}`);
        await module.shutdown();
        this.logger.info(`✅ Module '${module.name}' shutdown successfully`);
      } catch (error) {
        this.logger.error(`Failed to shutdown module '${module.name}':`, error);
      }
    }

    this.isInitialized = false;
    this.logger.info('🔧 All modules shutdown');
  }

  /**
   * Get a specific module by name
   */
  public getModule<T extends IModule>(name: string): T | undefined {
    return this.modules.get(name) as T;
  }

  /**
   * Get all loaded modules
   */
  public getLoadedModules(): IModule[] {
    return Array.from(this.modules.values());
  }

  /**
   * Check if a module is loaded
   */
  public isModuleLoaded(name: string): boolean {
    return this.modules.has(name);
  }

  /**
   * Resolve module dependencies and determine load order
   */
  private resolveLoadOrder(): void {
    const visited = new Set<string>();
    const visiting = new Set<string>();
    const loadOrder: string[] = [];

    const visit = (moduleName: string): void => {
      if (visiting.has(moduleName)) {
        throw new Error(`Circular dependency detected involving module '${moduleName}'`);
      }

      if (visited.has(moduleName)) {
        return;
      }

      const module = this.modules.get(moduleName);
      if (!module) {
        throw new Error(`Module '${moduleName}' not found`);
      }

      visiting.add(moduleName);

      // Visit dependencies first
      if (module.dependencies) {
        for (const dependency of module.dependencies) {
          if (!this.modules.has(dependency)) {
            throw new Error(
              `Module '${moduleName}' depends on '${dependency}' which is not registered`
            );
          }
          visit(dependency);
        }
      }

      visiting.delete(moduleName);
      visited.add(moduleName);
      loadOrder.push(moduleName);
    };

    // Visit all modules
    for (const moduleName of this.modules.keys()) {
      visit(moduleName);
    }

    this.loadOrder.length = 0;
    this.loadOrder.push(...loadOrder);

    this.logger.debug('Module load order resolved:', this.loadOrder);
  }

  /**
   * Get module loading statistics
   */
  public getStats(): Record<string, unknown> {
    return {
      totalModules: this.modules.size,
      loadedModules: this.isInitialized ? this.modules.size : 0,
      loadOrder: this.loadOrder,
      isInitialized: this.isInitialized,
    };
  }
}
