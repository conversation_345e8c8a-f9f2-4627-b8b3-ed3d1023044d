import dotenv from 'dotenv';
import { Logger } from '@/utils/Logger';
import { DatabaseConfig } from '@/types';

/**
 * Configuration manager for server settings
 * Loads from environment variables and validates configuration
 */
export class ConfigManager {
  private static instance: ConfigManager;
  private readonly logger: Logger;
  private config: ServerConfig | null = null;

  private constructor() {
    this.logger = Logger.getInstance();
  }

  public static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }

  /**
   * Load configuration from environment variables
   */
  public async load(): Promise<void> {
    try {
      // Load .env file
      dotenv.config();

      this.config = {
        server: {
          nodeEnv: process.env.NODE_ENV || 'development',
          port: parseInt(process.env.PORT || '3000', 10),
          host: process.env.HOST || 'localhost',
        },
        database: {
          type: (process.env.DB_TYPE as 'mysql' | 'postgresql') || 'mysql',
          host: process.env.DB_HOST || 'localhost',
          port: parseInt(process.env.DB_PORT || '3306', 10),
          username: process.env.DB_USERNAME || 'root',
          password: process.env.DB_PASSWORD || '',
          database: process.env.DB_DATABASE || 'rage_rp',
          synchronize: process.env.DB_SYNCHRONIZE === 'true',
          logging: process.env.DB_LOGGING === 'true',
        },
        jwt: {
          secret: process.env.JWT_SECRET || 'default-secret',
          expiresIn: process.env.JWT_EXPIRES_IN || '7d',
          refreshSecret: process.env.JWT_REFRESH_SECRET || 'default-refresh-secret',
          refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
        },
        security: {
          bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
          corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
          rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW || '15', 10),
          rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX || '100', 10),
        },
        ragemp: {
          host: process.env.RAGEMP_HOST || 'localhost',
          port: parseInt(process.env.RAGEMP_PORT || '22005', 10),
          maxPlayers: parseInt(process.env.RAGEMP_MAX_PLAYERS || '200', 10),
        },
        redis: {
          host: process.env.REDIS_HOST || 'localhost',
          port: parseInt(process.env.REDIS_PORT || '6379', 10),
          password: process.env.REDIS_PASSWORD || '',
          db: parseInt(process.env.REDIS_DB || '0', 10),
        },
        logging: {
          level: process.env.LOG_LEVEL || 'info',
          file: process.env.LOG_FILE !== 'false',
          console: process.env.LOG_CONSOLE !== 'false',
        },
        development: {
          debug: process.env.DEBUG === 'true',
          hotReload: process.env.HOT_RELOAD === 'true',
        },
      };

      this.validateConfig();
      this.logger.info('Configuration loaded successfully');

    } catch (error) {
      this.logger.error('Failed to load configuration:', error);
      throw error;
    }
  }

  /**
   * Get configuration value by path
   */
  public get<T>(path: string): T {
    if (!this.config) {
      throw new Error('Configuration not loaded');
    }

    const keys = path.split('.');
    let value: unknown = this.config;

    for (const key of keys) {
      if (typeof value === 'object' && value !== null && key in value) {
        value = (value as Record<string, unknown>)[key];
      } else {
        throw new Error(`Configuration path '${path}' not found`);
      }
    }

    return value as T;
  }

  /**
   * Get database configuration
   */
  public getDatabaseConfig(): DatabaseConfig {
    return this.get<DatabaseConfig>('database');
  }

  /**
   * Get server configuration
   */
  public getServerConfig(): ServerConfig['server'] {
    return this.get<ServerConfig['server']>('server');
  }

  /**
   * Check if running in development mode
   */
  public isDevelopment(): boolean {
    return this.get<string>('server.nodeEnv') === 'development';
  }

  /**
   * Check if running in production mode
   */
  public isProduction(): boolean {
    return this.get<string>('server.nodeEnv') === 'production';
  }

  /**
   * Validate configuration values
   */
  private validateConfig(): void {
    if (!this.config) {
      throw new Error('Configuration is null');
    }

    // Validate required fields
    const requiredFields = [
      'database.host',
      'database.username',
      'database.database',
      'jwt.secret',
    ];

    for (const field of requiredFields) {
      try {
        const value = this.get<string>(field);
        if (!value || value.trim() === '') {
          throw new Error(`Required configuration field '${field}' is empty`);
        }
      } catch {
        throw new Error(`Required configuration field '${field}' is missing`);
      }
    }

    // Validate JWT secret strength
    const jwtSecret = this.get<string>('jwt.secret');
    if (jwtSecret === 'default-secret' && this.isProduction()) {
      throw new Error('JWT secret must be changed in production');
    }

    // Validate database type
    const dbType = this.get<string>('database.type');
    if (!['mysql', 'postgresql'].includes(dbType)) {
      throw new Error(`Unsupported database type: ${dbType}`);
    }

    this.logger.info('Configuration validation passed');
  }
}

interface ServerConfig {
  server: {
    nodeEnv: string;
    port: number;
    host: string;
  };
  database: DatabaseConfig;
  jwt: {
    secret: string;
    expiresIn: string;
    refreshSecret: string;
    refreshExpiresIn: string;
  };
  security: {
    bcryptRounds: number;
    corsOrigin: string;
    rateLimitWindow: number;
    rateLimitMax: number;
  };
  ragemp: {
    host: string;
    port: number;
    maxPlayers: number;
  };
  redis: {
    host: string;
    port: number;
    password: string;
    db: number;
  };
  logging: {
    level: string;
    file: boolean;
    console: boolean;
  };
  development: {
    debug: boolean;
    hotReload: boolean;
  };
}
